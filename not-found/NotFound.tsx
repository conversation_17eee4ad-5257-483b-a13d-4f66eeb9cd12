import { Button, Result } from 'antd';
import Link from 'next/link';

interface NotFoundProps {
  title?: string;
  goBackLink?: string;
}
export function NotFound({ title, goBackLink }: NotFoundProps) {
  let mainTitle = title ?? 'Không tìm thấy khóa học';
  return (
    <Result
      title={mainTitle}
      status={404}
      extra={
        <Link href={goBackLink ? goBackLink : '/'}>
          <Button type={'primary'}>Trở về</Button>
        </Link>
      }
    />
  );
}
