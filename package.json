{"name": "studify", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack -p 3000 --hostname local.studify.vn --experimental-https", "build": "next build", "start": "next start -p 3000", "lint": "next lint", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build"}, "dependencies": {"@ant-design/nextjs-registry": "^1.0.2", "@ant-design/v5-patch-for-react-19": "^1.0.3", "@dnd-kit/core": "^6.3.1", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@headlessui/react": "^2.1.10", "@hello-pangea/dnd": "^17.0.0", "@heroicons/react": "^2.1.5", "@hookform/resolvers": "^5.2.1", "@redux-devtools/extension": "^3.3.0", "@reduxjs/toolkit": "^2.2.7", "@smakss/react-scroll-direction": "^4.2.0", "@tailwindcss/line-clamp": "^0.4.4", "@tailwindcss/typography": "^0.5.14", "@tanstack/react-table": "^8.20.1", "@tinymce/tinymce-react": "^5.1.1", "@tiptap/extension-bubble-menu": "^3.0.9", "@tiptap/extension-character-count": "^3.1.0", "@tiptap/extension-color": "^3.0.9", "@tiptap/extension-link": "^3.0.9", "@tiptap/extension-text-style": "^3.0.9", "@tiptap/extension-underline": "^3.0.9", "@tiptap/react": "^3.0.9", "@tiptap/starter-kit": "^3.0.9", "antd": "^5.22.7", "axios": "^1.7.7", "chart.js": "^4.4.3", "chartjs-plugin-datalabels": "^2.2.0", "cookies-next": "^4.2.1", "crypto-js": "^4.2.0", "dayjs": "^1.11.12", "dompurify": "^3.2.6", "immer": "^10.1.1", "lodash-es": "^4.17.21", "next": "15.4.1", "next-nprogress-bar": "^2.3.13", "query-string": "^9.1.0", "react": "19.1.0", "react-card-flip": "^1.2.3", "react-chartjs-2": "^5.2.0", "react-compound-slider": "^3.4.0", "react-confetti": "^6.1.0", "react-contenteditable": "^3.3.7", "react-dom": "19.1.0", "react-h5-audio-player": "^3.9.3", "react-hook-form": "^7.60.0", "react-joyride": "^3.0.0-7", "react-player": "^2.16.0", "react-query": "^3.39.3", "react-quill": "^2.0.0", "react-quill-new": "^3.4.6", "react-redux": "^9.1.2", "react-video-seek-slider": "^7.0.0", "redux-persist": "^6.0.0", "sass": "^1.77.8", "screenfull": "^6.0.2", "sharp": "^0.33.5", "swiper": "^11.1.9", "ts-pattern": "^5.7.0", "zod": "^4.0.5", "zustand": "^5.0.7"}, "devDependencies": {"@hookform/devtools": "^4.4.0", "@storybook/addon-essentials": "^8.6.12", "@storybook/addon-interactions": "^8.6.12", "@storybook/addon-onboarding": "^8.6.12", "@storybook/blocks": "^8.6.12", "@storybook/nextjs": "^8.6.12", "@storybook/react": "^8.6.12", "@storybook/test": "^8.6.12", "@types/crypto-js": "^4.2.2", "@types/lodash-es": "^4.17.12", "@types/node": "^20", "@types/react": "19.1.8", "@types/react-dom": "19.1.6", "@types/react-grid-layout": "^1.3.5", "@typescript-eslint/eslint-plugin": "^8.29.0", "@typescript-eslint/parser": "^8.29.0", "clsx": "^2.1.1", "eslint": "^8", "eslint-config-next": "15.4.1", "eslint-config-prettier": "^10.1.1", "eslint-plugin-import": "^2.31.0", "eslint-plugin-prettier": "^5.2.6", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-storybook": "^0.12.0", "eslint-plugin-tailwindcss": "^3.18.0", "postcss": "^8", "prettier": "^3.5.3", "prettier-plugin-tailwindcss": "^0.6.11", "storybook": "^8.6.12", "tailwind-merge": "^3.2.0", "tailwindcss": "^3.4.1", "typescript": "5.4.5"}, "resolutions": {"@types/react": "19.1.8", "@types/react-dom": "19.1.6"}}