'use client';

import { useEffect, useRef } from 'react';
import { useMutation } from 'react-query';
import { useCourse } from './apis/course';
import { useUserProgress } from './useUserProgress';

export interface UseVideoProgressTrackingProps {
  courseId: string;
  lectureId: string;
  sectionId: string;
  currentTime: number;
  isPlaying: boolean;
  isPreviewMode?: boolean;
  userId?: string;
  onProgressLoaded?: (savedTime: number) => void;
}

export const useVideoProgressTracking = ({
  courseId,
  lectureId,
  sectionId,
  currentTime,
  isPlaying,
  isPreviewMode = false,
  userId,
  onProgressLoaded,
}: UseVideoProgressTrackingProps) => {
  const intervalRef = useRef<NodeJS.Timeout | null>(null);
  const lastTrackedTimeRef = useRef<number>(0);
  const currentTimeRef = useRef<number>(currentTime);

  // Update the ref whenever currentTime changes
  useEffect(() => {
    currentTimeRef.current = currentTime + 1;
  }, [currentTime]);

  const { trackCourseProgress } = useCourse();

  // Mutation for tracking progress
  const { mutate: mutateTrackProgress } = useMutation(trackCourseProgress, {
    onError: (error) => {
      console.warn('Failed to track video progress:', error);
    },
  });

  // Fetch user's saved progress
  const { data: userProgress, isLoading: isLoadingProgress } = useUserProgress({
    userId,
    lectureId,
    enabled: !isPreviewMode && !!userId && !!lectureId,
  });

  // Load saved progress when available
  useEffect(() => {
    if (userProgress && userProgress.currentTime > 0 && onProgressLoaded) {
      // Call onProgressLoaded with the saved time
      onProgressLoaded(userProgress.currentTime);
    }
  }, [userProgress, onProgressLoaded]);

  useEffect(() => {
    // Start interval to track progress every 5 seconds
    if (isPreviewMode) return;

    intervalRef.current = setInterval(() => {
      if (!courseId || !lectureId || !sectionId) {
        return;
      }

      mutateTrackProgress({
        courseId,
        lectureId,
        sectionId,
        currentTime: Math.floor(currentTimeRef.current),
      });

      lastTrackedTimeRef.current = currentTimeRef.current;
    }, 5000);

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
        intervalRef.current = null;
      }
    };
  }, [courseId, lectureId, sectionId]);

  // Clean up on unmount
  useEffect(() => {
    return () => {
      if (isPreviewMode) return;

      mutateTrackProgress({
        courseId,
        lectureId,
        sectionId,
        currentTime: Math.floor(currentTimeRef.current),
      });

      if (intervalRef.current) {
        clearInterval(intervalRef.current);
        intervalRef.current = null;
      }
    };
  }, [lectureId]);

  return {
    userProgress,
    isLoadingProgress,
  };
};
