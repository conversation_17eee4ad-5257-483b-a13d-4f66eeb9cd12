import { API_ENDPOINTS, HttpMethod } from '@/constants/api';
import { fetcher } from '@/lib/fetcher';
import queryString from 'query-string';

const useCompleteCreatorTutorial = () => {
  const completeCreatorTutorial = async (payload: { id: string }) => {
    const query = queryString.stringifyUrl({
      url: API_ENDPOINTS.USERS.PATCH.COMPLETE_TUTORIAL,
      query: { id: payload.id },
    });
    const res = await fetcher(query, { method: HttpMethod.PATCH });
    return res;
  };

  return { completeCreatorTutorial };
};

export default useCompleteCreatorTutorial;
