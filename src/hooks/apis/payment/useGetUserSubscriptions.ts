import { getUserSubscriptions } from '@/services/payment.service';
import { useQuery } from 'react-query';
import { PAYMENT_QUERY_KEYS } from './useGetPlans';

export const useGetUserSubscriptions = (status?: 'ACTIVE' | 'INACTIVE' | 'CANCELLED' | 'PENDING') => {
  return useQuery({
    queryKey: [PAYMENT_QUERY_KEYS.USER_SUBSCRIPTIONS, status],
    queryFn: () => getUserSubscriptions(status),
    staleTime: 5 * 60 * 1000, // 5 minutes
    cacheTime: 10 * 60 * 1000, // 10 minutes
  });
};

// Convenience hook to get active subscriptions only
export const useGetActiveSubscriptions = () => {
  return useGetUserSubscriptions('ACTIVE');
};