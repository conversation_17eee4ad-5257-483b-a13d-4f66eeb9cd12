import { useNotification } from '@/hooks';
import { changePaymentMethod, ChangePaymentMethodRequest, InitSubscriptionResponse } from '@/services/payment.service';
import { useCallback, useState } from 'react';
import { useMutation, useQueryClient } from 'react-query';
import { PAYMENT_QUERY_KEYS } from './useGetPlans';

export const useChangePaymentMethod = () => {
  const notification = useNotification();
  const queryClient = useQueryClient();
  const [paymentData, setPaymentData] = useState<InitSubscriptionResponse | null>(null);

  const changePaymentMethodMutation = useMutation({
    mutationFn: (data: ChangePaymentMethodRequest) => changePaymentMethod(data),
    onSuccess: (data) => {
      // Store payment data for VNPay POST form submission
      setPaymentData(data);
      console.log('data', data);
      // Invalidate and refetch payment-related queries
      queryClient.invalidateQueries([PAYMENT_QUERY_KEYS.USER_SUBSCRIPTIONS]);
      queryClient.invalidateQueries([PAYMENT_QUERY_KEYS.TRANSACTIONS]);
    },
    onError: (error: any) => {
      const errorMessage = error?.message || 'Có lỗi xảy ra khi thay đổi phương thức thanh toán';
      notification.error({ message: errorMessage });
    },
  });

  const startChangePaymentMethod = useCallback(
    (data: ChangePaymentMethodRequest) => {
      return changePaymentMethodMutation.mutateAsync(data);
    },
    [changePaymentMethodMutation],
  );

  const clearPaymentData = useCallback(() => {
    setPaymentData(null);
  }, []);

  return {
    startChangePaymentMethod,
    paymentData,
    clearPaymentData,
    isLoading: changePaymentMethodMutation.isLoading,
    isError: changePaymentMethodMutation.isError,
    error: changePaymentMethodMutation.error,
  };
};
