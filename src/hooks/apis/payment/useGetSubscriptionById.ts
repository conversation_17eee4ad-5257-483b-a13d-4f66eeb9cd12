import { getSubscriptionById } from '@/services/payment.service';
import { useQuery } from 'react-query';

export const useGetSubscriptionById = (subscriptionId: string | null) => {
  return useQuery(
    ['subscription', subscriptionId],
    () => {
      if (!subscriptionId) {
        throw new Error('Subscription ID is required');
      }
      return getSubscriptionById(subscriptionId);
    },
    {
      enabled: !!subscriptionId,
      retry: false,
    }
  );
};