import { getTransactionReceipt } from '@/services/payment.service';
import { useQuery } from 'react-query';
import { PAYMENT_QUERY_KEYS } from './useGetPlans';

export const useGetTransactionReceipt = (subscriptionId: string, transactionId: string, enabled: boolean = false) => {
  return useQuery({
    queryKey: [PAYMENT_QUERY_KEYS.TRANSACTION_RECEIPT, subscriptionId, transactionId],
    queryFn: () => getTransactionReceipt(subscriptionId, transactionId),
    enabled: enabled && !!subscriptionId && !!transactionId,
    staleTime: 5 * 60 * 1000, // 5 minutes
    cacheTime: 10 * 60 * 1000, // 10 minutes
  });
};