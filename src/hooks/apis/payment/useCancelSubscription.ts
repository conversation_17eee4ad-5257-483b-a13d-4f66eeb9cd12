import { cancelSubscription } from '@/services/payment.service';
import { useMutation, useQueryClient } from 'react-query';
import { PAYMENT_QUERY_KEYS } from './useGetPlans';

export const useCancelSubscription = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (subscriptionId: string) => cancelSubscription(subscriptionId),
    onSuccess: () => {
      // Invalidate and refetch subscription-related queries
      queryClient.invalidateQueries([PAYMENT_QUERY_KEYS.USER_SUBSCRIPTIONS]);
      queryClient.invalidateQueries([PAYMENT_QUERY_KEYS.TRANSACTIONS]);
    },
  });
};