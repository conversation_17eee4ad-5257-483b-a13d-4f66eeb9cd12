import { getPaymentPlans } from '@/services/payment.service';
import { useQuery } from 'react-query';

export const PAYMENT_QUERY_KEYS = {
  PLANS: 'payment-plans',
  TAX_RATE: 'tax-rate',
  USER_SUBSCRIPTIONS: 'user-subscriptions',
  TRANSACTIONS: 'transactions',
  TRANSACTION_RECEIPT: 'transaction-receipt',
} as const;

export const useGetPlans = () => {
  return useQuery({
    queryKey: [PAYMENT_QUERY_KEYS.PLANS],
    queryFn: getPaymentPlans,
    staleTime: 5 * 60 * 1000, // 5 minutes
    cacheTime: 10 * 60 * 1000, // 10 minutes
  });
};