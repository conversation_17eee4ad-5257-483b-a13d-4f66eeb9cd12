import { useNotification } from '@/hooks';
import { InitSubscriptionRequest, InitSubscriptionResponse, initSubscription } from '@/services/payment.service';
import { useCallback, useState } from 'react';
import { useMutation } from 'react-query';

export const useInitSubscription = () => {
  const notification = useNotification();
  const [paymentData, setPaymentData] = useState<InitSubscriptionResponse | null>(null);

  const initSubscriptionMutation = useMutation({
    mutationFn: (data: InitSubscriptionRequest) => {
      return initSubscription(data);
    },
    onSuccess: (data) => {
      // Store payment data for VNPay POST form or direct redirect
      setPaymentData(data);

      // If no VNPay data, fall back to direct URL redirect
      // if (!data.ispTxnId && data.payment_url) {
      //   window.location.href = data.payment_url;
      // }
    },
    onError: (error: any) => {
      const errorMessage = error?.message || 'Có lỗi xảy ra khi khởi tạo thanh toán';
      notification.error({ message: errorMessage });
    },
  });

  const startPayment = useCallback(
    (data: InitSubscriptionRequest) => {
      return initSubscriptionMutation.mutateAsync(data);
    },
    [initSubscriptionMutation],
  );

  const clearPaymentData = useCallback(() => {
    setPaymentData(null);
  }, []);

  return {
    startPayment,
    paymentData,
    clearPaymentData,
    isLoading: initSubscriptionMutation.isLoading,
    isError: initSubscriptionMutation.isError,
    error: initSubscriptionMutation.error,
  };
};
