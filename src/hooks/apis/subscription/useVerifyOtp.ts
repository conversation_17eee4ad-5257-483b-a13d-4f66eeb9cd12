import { useNotification } from '@/hooks';
import { VerifyOtpRequest, verifySubscriptionOtp } from '@/services/subscription.service';
import { useCallback } from 'react';
import { useMutation } from 'react-query';

export const useVerifyOtp = () => {
  const notification = useNotification();

  const verifyOtpMutation = useMutation({
    mutationFn: (data: VerifyOtpRequest) => verifySubscriptionOtp(data),
    onSuccess: (data) => {
      notification.success({ message: 'OTP xác thực thành công' });
    },
    onError: (error: any) => {
      const errorMessage = 'Mã OTP không đúng. Vui lòng kiểm tra lại hoặc yêu cầu mã mới.';
      notification.error({ message: errorMessage });
    },
  });

  const verifyOtp = useCallback(
    (email: string, otp: string, type: string = 'SUBSCRIPTION') => {
      return verifyOtpMutation.mutateAsync({ email, otp, type });
    },
    [verifyOtpMutation],
  );

  return {
    verifyOtp,
    isLoading: verifyOtpMutation.isLoading,
  };
};
