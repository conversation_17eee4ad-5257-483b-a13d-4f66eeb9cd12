import { useNotification } from '@/hooks';
import { RequestOtpRequest, requestSubscriptionOtp } from '@/services/subscription.service';
import { useCallback } from 'react';
import { useMutation } from 'react-query';

export const useRequestOtp = () => {
  const notification = useNotification();

  const requestOtpMutation = useMutation({
    mutationFn: (data: RequestOtpRequest) => requestSubscriptionOtp(data),
    onSuccess: (data) => {
      notification.success({ message: 'OTP đã được gửi đến email của bạn' });
    },
    onError: (error: any) => {
      const errorMessage = error?.message || 'Có lỗi xảy ra khi gửi OTP';
      notification.error({ message: errorMessage });
    },
  });

  const requestOtp = useCallback(
    (email: string) => {
      return requestOtpMutation.mutateAsync({ email });
    },
    [requestOtpMutation]
  );

  return {
    requestOtp,
    isLoading: requestOtpMutation.isLoading,
  };
};