'use client';

import { API_ENDPOINTS } from '@/constants/api';
import { CourseListBase } from '@/features/courses/types/common.type';
import { CourseInfo } from '@/features/courses/types/course.type';
import { fetcher } from '@/lib/fetcher';
import queryString from 'query-string';
import { useState } from 'react';
import { useQuery } from 'react-query';

interface UseCreatorCoursesParams {
  limit?: number;
  page?: number;
  publish?: number;
  name?: string;
}

interface UseCreatorCoursesResult {
  courses: CourseInfo[];
  totalCount: number;
  currentPage: number;
  limit: number;
  isLoading: boolean;
  isError: boolean;
  error: any;
  refetch: () => void;
  searchName: string;
  setSearchName: (name: string) => void;
  openEmailActivationModal: boolean;
  setOpenEmailActivationModal: (open: boolean) => void;
}

const fetchAllCoursesOfUser = async (params: UseCreatorCoursesParams) => {
  const { page = 0, limit = 10, publish, name } = params;

  const queryParams: Record<string, any> = { page, limit };
  if (publish !== undefined) queryParams.publish = publish;
  if (name && name.trim()) queryParams.name = name.trim();

  const query = queryString.stringifyUrl({
    url: API_ENDPOINTS.COURSES.GET.ALL_COURSES,
    query: queryParams,
  });

  const response = await fetcher<CourseListBase<CourseInfo>>(query);
  return response.data as CourseListBase<CourseInfo>;
};

export const useCreatorCourses = (
  initialParams: Omit<UseCreatorCoursesParams, 'name'> = {},
): UseCreatorCoursesResult => {
  const [searchName, setSearchName] = useState('');
  const { limit = 10, page = 0, publish } = initialParams;

  const [openEmailActivationModal, setOpenEmailActivationModal] = useState(false);

  const { data, isLoading, isError, error, refetch } = useQuery(
    ['creatorCourses', page, limit, publish, searchName],
    () => fetchAllCoursesOfUser({ page, limit, publish, name: searchName }),
    {
      keepPreviousData: true,
      staleTime: 5 * 60 * 1000, // 5 minutes
      onError: (err) => {
        console.log('err: ', err);
      },
    },
  );

  return {
    courses: data?.data || [],
    totalCount: data?.count || 0,
    currentPage: data?.page || 0,
    limit: data?.limit || limit,
    isLoading,
    isError,
    error,
    refetch,
    searchName,
    setSearchName,
    openEmailActivationModal,
    setOpenEmailActivationModal,
  };
};
