export function secondsToTimestamp(seconds: number) {
  if (!seconds || seconds <= 0) return 0;
  const millisecond = seconds * 1000;
  const currentTimestamp = Date.now();
  return Math.floor(currentTimestamp + millisecond);
}

export function timestampToSeconds(timestamp: number) {
  if (!timestamp || timestamp <= 0) return 0;
  const currentTimestamp = Date.now();
  const millisecond = timestamp - currentTimestamp;
  return Math.floor(millisecond / 1000);
}

export function secondToHHMMSS(timeInSeconds: number) {
  const totalSeconds = Math.ceil(timeInSeconds);
  const hours = Math.floor(totalSeconds / 3600);
  const minutes = Math.floor((totalSeconds % 3600) / 60);
  const seconds = totalSeconds % 60;

  if (hours) {
    return `${hours}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
  }

  if (minutes) {
    return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
  }

  return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
}

export function secondsToHHMMSSTextFormat(seconds: number) {
  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const remainingSeconds = seconds % 60;

  return `${hours}h${minutes}m${remainingSeconds}s`;
}

export const convertSecondsToHour = (seconds: number) => {
  return Math.floor(seconds / 3600);
};
