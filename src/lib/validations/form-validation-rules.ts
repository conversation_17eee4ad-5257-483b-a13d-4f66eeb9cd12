export const REGEX_RULES = {
  WHITE_SPACE: /\s/,
  PASSWORD:
    /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[!@#$%^&*()_+\-=\\[\]{};':"\\|,.<>\\/?])[A-Za-z\d!@#$%^&*()_+\-=\\[\]{};':"\\|,.<>\\/?]{8,}$/,
};

export const VALIDATION_MESSAGES = {
  PASSWORD: {
    MESSAGE: 'Mật khẩu phải có ít nhất 8 ký tự, bao gồm chữ hoa, chữ thường và số.',
  },
};

export const ERROR_MESSAGES = {
  NAME: {
    REQUIRED: 'Vui lòng nhập tên người dùng',
    MIN: 'Tên người dùng tối thiểu 5 ký tự',
  },
  PASSWORD: {
    REQUIRED: '<PERSON>ui lòng nhập mật khẩu',
    GENERAL: '<PERSON><PERSON>t khẩu phải bao gồm chữ hoa, số và ký tự đặc biệt',
    WHITE_SPACE: 'Mật khẩu không được chứa khoảng trắng',
    MIN: 'Mật khẩu tối thiểu 8 ký tự',
  },
};
