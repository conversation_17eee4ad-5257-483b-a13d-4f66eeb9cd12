'use client';

import Button from '@/components/ui/button/button';
import Typography from '@/components/ui/typography/typography';
import { useGetSubscriptionById } from '@/hooks/apis/payment/useGetSubscriptionById';
import Image from 'next/image';
import { useRouter, useSearchParams } from 'next/navigation';
import { useEffect, useState } from 'react';

function PaymentResult() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const subscriptionId = searchParams?.get('subscription_id');
  const [redirectUrl, setRedirectUrl] = useState<string | null>(null);

  const { data: subscription, isLoading, error } = useGetSubscriptionById(subscriptionId);

  // Check for redirectUrl in localStorage
  useEffect(() => {
    if (typeof window !== 'undefined') {
      const storedUrl = localStorage.getItem('redirectUrl');
      setRedirectUrl(storedUrl);
    }
  }, []);

  // Refresh the page once after successful payment to sync user context
  useEffect(() => {
    if (subscription && !isLoading && !error) {
      // Use a timeout to allow the component to render first
      const timer = setTimeout(() => {
        window.location.reload();
      }, 0);

      // Only reload once - set a flag in sessionStorage
      const hasReloaded = sessionStorage.getItem('payment-success-reloaded');
      if (hasReloaded) {
        clearTimeout(timer);
        return;
      }

      sessionStorage.setItem('payment-success-reloaded', 'true');

      return () => clearTimeout(timer);
    }
  }, [subscription, isLoading, error]);

  const handleLoginRedirect = () => {
    // Clear the reload flag when user navigates away
    sessionStorage.removeItem('payment-success-reloaded');
    router.push('/');
  };

  const handleCourseDetailRedirect = () => {
    // Clear the reload flag and redirectUrl when user navigates to course detail
    sessionStorage.removeItem('payment-success-reloaded');
    if (typeof window !== 'undefined' && redirectUrl) {
      localStorage.removeItem('redirectUrl');
      window.location.href = redirectUrl;
    }
  };

  if (isLoading) {
    return (
      <div className="flex min-h-screen items-center justify-center bg-neutral-50">
        <Typography variant="bodyLg">Loading subscription details...</Typography>
      </div>
    );
  }

  if (error || !subscription) {
    return (
      <div className="flex min-h-screen items-center justify-center bg-neutral-50">
        <Typography variant="bodyLg" className="text-red-500">
          Failed to load subscription details
        </Typography>
      </div>
    );
  }

  // Format dates
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('vi-VN');
  };

  const formatDateTime = (dateString: string) => {
    return new Date(dateString).toLocaleString('vi-VN');
  };

  // Calculate total amount
  const totalAmount = subscription.netAmount + subscription.taxAmount;

  return (
    <div className="bg-gray-50 min-h-screen bg-neutral-50">
      <div className="grid min-h-screen grid-cols-1 gap-8 md:grid-cols-10">
        <div className="ml-auto mr-[120px] mt-8 max-w-[655px] pt-24 md:col-span-6">
          <div className="">
            {/* Success Icon */}
            <div className="mb-8 flex">
              <div className="relative">
                <div className="flex h-[120px] w-[120px] items-center rounded-full bg-green-100">
                  <Image src="/images/payment/check.png" alt="check" width={96} height={96} />
                </div>
              </div>
            </div>

            {/* Success Message */}
            <div className="flex flex-col">
              <Typography variant="headlineLg" className="text-gray-900 mb-6">
                Woohoo! Thanks for your payment
              </Typography>
              <Typography variant="headlineXs" className="text-gray-600 mb-5">
                You're now a member on Studify.
              </Typography>
            </div>

            {/* Benefits List */}
            <div className="mb-8">
              <ul className="space-y-3">
                <li className="flex items-start">
                  <span className="mr-3 mt-1">•</span>
                  <Typography variant="bodyLg" className="text-gray-700">
                    Unlimited courses & content uploads
                  </Typography>
                </li>
                <li className="flex items-start">
                  <span className="mr-3 mt-1">•</span>
                  <Typography variant="bodyLg" className="text-gray-700">
                    Full access to analytics & insights
                  </Typography>
                </li>
                <li className="flex items-start">
                  <span className="mr-3 mt-1">•</span>
                  <Typography variant="bodyLg" className="text-gray-700">
                    Priority support
                  </Typography>
                </li>
                <li className="flex items-start">
                  <span className="mr-3 mt-1">•</span>
                  <Typography variant="bodyLg" className="text-gray-700">
                    Exclusive creator community access
                  </Typography>
                </li>
              </ul>
            </div>

            {/* Receipt Info */}
            <div className="mb-8 flex flex-col">
              <Typography variant="bodyMd" className="text-gray-600 mb-6">
                📧 <strong>Receipt sent to your email:</strong>{' '}
                <span className="text-blue-600">{subscription.createdBy?.email}</span>
              </Typography>
              <Typography variant="bodyMd" className="text-gray-600 mb-8">
                If you have any questions or need support, please contact us at{' '}
                <span className="text-blue-600"><EMAIL></span>
              </Typography>
            </div>

            {/* Action Buttons */}
            <div className="flex flex-col gap-4 sm:flex-row">
              <Button variant="primary" className="h-[48px] flex-1" onClick={handleLoginRedirect}>
                Đăng nhập để học ngay
              </Button>
              {redirectUrl && (
                <Button variant="secondary" className="h-[48px] flex-1" onClick={handleCourseDetailRedirect}>
                  Trở về Chi tiết khoá học
                </Button>
              )}
            </div>
          </div>
        </div>

        {/* Transaction Details Sidebar */}
        <div className="bg-white md:col-span-4">
          <div className="ml-20 mt-40 max-w-[364px]">
            <div className="rounded-xl bg-white p-6">
              <div className="border-b border-neutral-100 pb-3">
                <Typography variant="labelMd">THÔNG TIN GIAO DỊCH</Typography>
              </div>

              <div className="space-y-4 pt-3">
                <div className="flex items-center justify-between">
                  <div className="flex flex-col">
                    <Typography variant="labelLg">Gói đăng ký</Typography>
                    <Typography variant="headlineXs">{subscription.planPrice.name}</Typography>
                  </div>
                  <div className="flex flex-col">
                    <Typography variant="labelLg" className="text-right">
                      {formatDate(subscription.startDate)}
                    </Typography>
                    <Typography variant="labelLg" className="">
                      — {subscription.nextBillingDate ? formatDate(subscription.nextBillingDate) : 'N/A'}
                    </Typography>
                  </div>
                </div>

                <div className="flex justify-between">
                  <div className="flex flex-col">
                    <Typography variant="labelLg">Số tiền đã thanh toán</Typography>
                    <Typography variant="labelXs">(Bao gồm VAT)</Typography>
                  </div>
                  <div className="text-right">
                    <Typography variant="labelLg">{totalAmount.toLocaleString('vi-VN')}đ</Typography>
                  </div>
                </div>

                <div className="flex justify-between">
                  <Typography variant="labelLg">Phương thức thanh toán</Typography>
                  <Typography variant="labelLg">
                    {subscription.paymentToken?.gateway} - {subscription.paymentToken?.cardType}
                  </Typography>
                </div>

                <div className="flex justify-between">
                  <Typography variant="labelLg">Thời gian thanh toán</Typography>
                  <Typography variant="labelLg">{formatDateTime(subscription.createdAt)}</Typography>
                </div>

                {/* <div className="border-t border-neutral-100 pt-4">
                  <div className="flex justify-between">
                    <Typography variant="labelLg">Mã giao dịch</Typography>
                    <Typography variant="titleMd">#{subscription.id}</Typography>
                  </div>
                </div> */}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default PaymentResult;
