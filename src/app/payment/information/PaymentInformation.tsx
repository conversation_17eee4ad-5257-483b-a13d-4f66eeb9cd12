'use client';

import { clearCookies, getAccessToken, getUserFromCookie } from '@/actions/cookieAction';
import Steps from '@/components/ui/steps/steps';
import { useNotification } from '@/hooks';
import { useRequestOtp, useVerifyOtp } from '@/hooks/apis/subscription';
import UserInfoContext from '@/utils/providers/UserInfoProvider';
import { redirectToBillingCycle } from '@/utils/url.util';
import { ArrowLeftOutlined } from '@ant-design/icons';
import { useRouter, useSearchParams } from 'next/navigation';
import React, { useContext, useEffect, useMemo, useRef, useState } from 'react';
import { useGetPlans, useGetTaxRate } from '../../../hooks/apis/payment';
import AccountInformation from './components/AccountInformation';
import AuthenticatedAccountInfo from './components/AuthenticatedAccountInfo';
import OrderSummary from './components/OrderSummary';
import OTPVerification from './components/OTPVerification';
import PaymentMethodSelection from './components/PaymentMethodSelection';

function PaymentInformation() {
  const router = useRouter();
  const notification = useNotification();
  const searchParams = useSearchParams();
  const { userInfo, setUserInfo } = useContext(UserInfoContext);
  const [email, setEmail] = useState('');
  const [selectedPaymentMethod, setSelectedPaymentMethod] = useState('vnpay');
  const [otpId, setOtpId] = useState('');
  const [otp, setOtp] = useState(['', '', '', '', '', '']);
  const [error, setError] = useState('');
  const [showOtpSection, setShowOtpSection] = useState(false);
  const [isOtpVerified, setIsOtpVerified] = useState(false);
  const [isChangingEmail, setIsChangingEmail] = useState(false);

  // Ref to track if we've already shown the toast for canceled status
  const hasShownCanceledToast = useRef(false);

  const { requestOtp, isLoading } = useRequestOtp();
  const { verifyOtp, isLoading: isVerifyingOtp } = useVerifyOtp();

  // Check if user is logged in and auto-verify
  const isLoggedIn = Boolean(userInfo?.info?.id && userInfo?.info?.email);

  useEffect(() => {
    if (isLoggedIn && userInfo?.info?.email && !isChangingEmail) {
      // User is logged in, use their email and skip verification
      setEmail(userInfo.info.email);
      setIsOtpVerified(true);
      setShowOtpSection(false);
    }
  }, [isLoggedIn, userInfo, isChangingEmail]);

  // Check for status=canceled in query params and show toast
  useEffect(() => {
    const status = searchParams?.get('status');
    if (status === 'canceled' && !hasShownCanceledToast.current) {
      hasShownCanceledToast.current = true;

      notification.error({
        message: 'Thanh toán không thành công',
        description:
          'Có lỗi xảy ra trong quá trình thanh toán. Vui lòng kiểm tra thông tin, phương thức thanh toán và thử lại.',
      });

      // Remove the status query parameter from URL
      const newSearchParams = new URLSearchParams(searchParams?.toString());
      newSearchParams.delete('status');
      const newUrl = `${window.location.pathname}${newSearchParams.toString() ? '?' + newSearchParams.toString() : ''}`;
      router.replace(newUrl);
    }
  }, [searchParams, notification, router]);

  const handleChangeEmail = async () => {
    // Clear cookies and reset states to allow email change flow
    try {
      await clearCookies();
      setUserInfo({ info: {} as any, token: '' });
    } catch (error) {
      console.error('Failed to clear cookies:', error);
    }

    setIsChangingEmail(true);
    setIsOtpVerified(false);
    setShowOtpSection(false);
    setEmail('');
    setError('');
    setOtp(['', '', '', '', '', '']);
  };

  const stepItems = [
    {
      title: 'Thông tin gói đăng ký',
      status: 'finish' as const,
    },
    {
      title: 'Thông tin thanh toán',
      status: 'process' as const,
    },
  ];

  const handleRequestOTP = async () => {
    if (!email) {
      setError('Email is required');
      return;
    }

    setError('');

    try {
      const data = await requestOtp(email);
      setOtpId(data.id);
      setShowOtpSection(true);
    } catch (error: any) {
      setError(error?.message || 'Failed to request OTP');
    }
  };

  const handleOtpChange = (index: number, value: string) => {
    if (value.length <= 1) {
      const newOtp = [...otp];
      newOtp[index] = value;
      setOtp(newOtp);

      // Auto focus next input
      if (value && index < 5) {
        const nextInput = document.getElementById(`otp-${index + 1}`);
        nextInput?.focus();
      }
    }
  };

  const handleOtpArrayChange = (newOtp: string[]) => {
    setOtp(newOtp);
  };

  const handleOtpKeyDown = (index: number, e: React.KeyboardEvent) => {
    if (e.key === 'Backspace' && !otp[index] && index > 0) {
      const prevInput = document.getElementById(`otp-${index - 1}`);
      prevInput?.focus();
    }
  };

  const handleResendOtp = async () => {
    try {
      const data = await requestOtp(email);
      setOtpId(data.id);
      setOtp(['', '', '', '', '', '']);
    } catch (error: any) {
      setError(error?.message || 'Failed to resend OTP');
    }
  };

  const handleVerifyOtp = async () => {
    const otpString = otp.join('');
    if (otpString.length !== 6) {
      setError('Please enter all 6 digits');
      return;
    }

    setError('');

    try {
      await verifyOtp(email, otpString, 'SUBSCRIPTION');
      setIsOtpVerified(true);

      // Refresh userInfo from updated cookies after successful OTP verification
      try {
        const { userInfoParsed } = await getUserFromCookie();
        const accessToken = await getAccessToken();
        const newUserInfo = { info: userInfoParsed, token: accessToken ?? '' };
        setUserInfo(newUserInfo);
      } catch (cookieError) {
        console.error('Failed to refresh user info:', cookieError);
      }
    } catch (error: any) {
      setError('Mã OTP không đúng. Vui lòng thử lại.');
    }
  };
  const selectedPlan = searchParams?.get('plan');
  const planPriceId = selectedPlan; // The plan query param is the plan_price_id

  const { data: plansData, isLoading: isLoadingPlans } = useGetPlans();
  const { data: taxRateData, isLoading: isLoadingTaxRate } = useGetTaxRate();

  const plans = useMemo(() => {
    if (!plansData || plansData.length === 0) return [];

    const plan = plansData[0]; // Assuming we get the "Pro Plan" from API
    if (!plan?.planPrices) return [];

    // Sort by billing cycle (12=yearly, 3=quarterly, 1=monthly)
    const sortedPrices = [...plan.planPrices].sort((a, b) => b.billingCycle - a.billingCycle);

    return sortedPrices.map((planPrice) => {
      const isYearly = planPrice.billingCycle === 12;
      const isQuarterly = planPrice.billingCycle === 3;
      const isMonthly = planPrice.billingCycle === 1;

      // Calculate monthly price and total price
      const monthlyPrice = planPrice.basePrice; // basePrice is already the monthly price
      const totalPrice = planPrice.basePrice * planPrice.billingCycle; // Total for the entire billing cycle

      // Calculate discount compared to monthly price
      const monthlyPlanPrice = sortedPrices.find((p) => p.billingCycle === 1)?.basePrice || planPrice.basePrice;
      const monthlyBasePrice = monthlyPlanPrice;
      const discount = monthlyBasePrice > 0 ? Math.round((1 - monthlyPrice / monthlyBasePrice) * 100) : 0;

      return {
        id: planPrice.id,
        title: isYearly ? 'Hàng năm' : isQuarterly ? 'Hàng quý' : 'Hàng tháng',
        price: `${monthlyPrice.toLocaleString('vi-VN')}đ/tháng`,
        totalPrice: planPrice.billingCycle > 1 ? `Thanh toán 1 lần: ${totalPrice.toLocaleString('vi-VN')}đ` : '',
        discount: discount > 0 ? `Tiết kiệm ${discount}%` : '',
        popular: isYearly,
        bgColor: isYearly ? '#2E2EE5' : 'white',
        borderColor: isYearly ? undefined : '#D4DAE5',
        textColor: isYearly ? 'white' : 'black',
        billingCycle: planPrice.billingCycle,
        basePrice: planPrice.basePrice,
        grandTotal: totalPrice,
      };
    });
  }, [plansData]);

  const orderSummary = useMemo(() => {
    const currentPlan = plans.find((p) => p.id === selectedPlan) as any;

    if (!currentPlan || !taxRateData) {
      return {
        planName: '',
        planDiscount: '',
        basePrice: 0,
        taxRate: 0,
        taxAmount: 0,
        totalAmount: 0,
      };
    }
    const basePrice = currentPlan.grandTotal; // Use total price for the entire billing cycle
    const taxRate = taxRateData.rate; // Rate is already a percentage (e.g., 8)
    const taxAmount = Math.round(basePrice * (taxRateData.rate / 100)); // Convert percentage to decimal for calculation
    const totalAmount = basePrice + taxAmount;

    return {
      planName: `Thanh toán ${currentPlan.title.toLowerCase()}`,
      planDiscount: currentPlan.discount ? `(${currentPlan.discount})` : '',
      basePrice,
      taxRate,
      taxAmount,
      totalAmount,
    };
  }, [plans, selectedPlan, taxRateData]);

  return (
    <div className="min-h-screen bg-neutral-50">
      <div className="grid min-h-screen grid-cols-1 gap-8 md:grid-cols-10">
        <div className="ml-auto mr-[120px] mt-8 max-w-[655px] pt-24 md:col-span-6">
          <div className="">
            {/* Header */}
            <div className="mb-8 flex items-center">
              <button
                className="mr-4 flex size-10 items-center justify-center rounded-lg border border-neutral-200 bg-white"
                onClick={() => redirectToBillingCycle(router)}
              >
                <ArrowLeftOutlined className="text-xl text-neutral-600" />
              </button>
            </div>

            <div className="md:col-span-2">
              {/* Steps */}
              <div className="mb-8 w-[429px]">
                <Steps current={1} items={stepItems} />
              </div>

              <div className="min-w-[655px]">
                {/* Account Information based on authentication status */}
                {isOtpVerified || (isLoggedIn && !isChangingEmail) ? (
                  // Authenticated user - show account info with change email option
                  <AuthenticatedAccountInfo email={userInfo?.info?.email || ''} onChangeEmail={handleChangeEmail} />
                ) : (
                  // Guest user or authenticated user changing email - show verification flow
                  <>
                    {!showOtpSection ? (
                      <AccountInformation
                        email={email}
                        onEmailChange={setEmail}
                        onContinue={handleRequestOTP}
                        error={error}
                        isLoading={isLoading}
                      />
                    ) : (
                      <OTPVerification
                        email={email}
                        otp={otp}
                        error={error}
                        isVerifying={isVerifyingOtp}
                        isResending={isLoading}
                        onOtpChange={handleOtpChange}
                        onOtpKeyDown={handleOtpKeyDown}
                        onVerify={handleVerifyOtp}
                        onResend={handleResendOtp}
                        onChangeEmail={() => {
                          setShowOtpSection(false);
                          setIsOtpVerified(false);
                          setError('');
                          setOtp(['', '', '', '', '', '']);
                        }}
                        onOtpArrayChange={handleOtpArrayChange}
                      />
                    )}
                  </>
                )}

                {/* Payment Method Selection */}
                <PaymentMethodSelection
                  selectedPaymentMethod={selectedPaymentMethod}
                  onPaymentMethodChange={setSelectedPaymentMethod}
                  isDisabled={!(isOtpVerified || (isLoggedIn && !isChangingEmail))}
                  planPriceId={planPriceId || undefined}
                />
              </div>
            </div>
          </div>
        </div>

        {/* Order Summary */}
        <OrderSummary orderSummary={orderSummary} />
      </div>
    </div>
  );
}

export default PaymentInformation;
