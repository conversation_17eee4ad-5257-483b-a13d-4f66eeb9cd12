'use client';

import VNPayPostForm from '@/components/payment/VNPayPostForm';
import Radio from '@/components/ui/radio/radio';
import RadioGroup from '@/components/ui/radio/radio-group';
import Typography from '@/components/ui/typography/typography';
import { useInitSubscription } from '@/hooks/apis/payment';
import { generateUniqueRequestKey } from '@/utils/payment';
import UserInfoContext from '@/utils/providers/UserInfoProvider';
import { useContext } from 'react';
import { Button } from '../../../../components/ui';

interface PaymentMethodSelectionProps {
  selectedPaymentMethod: string;
  onPaymentMethodChange: (method: string) => void;
  isDisabled: boolean;
  planPriceId?: string;
}

const PaymentMethodSelection = ({
  selectedPaymentMethod,
  onPaymentMethodChange,
  isDisabled,
  planPriceId,
}: PaymentMethodSelectionProps) => {
  const { userInfo } = useContext(UserInfoContext);
  const { startPayment, paymentData, clearPaymentData, isLoading: isPaymentLoading } = useInitSubscription();

  const handleConfirmPayment = async () => {
    if (!userInfo?.info?.id || !planPriceId) {
      console.error('Missing user ID or plan price ID');
      return;
    }

    try {
      const requestKey = generateUniqueRequestKey(userInfo.info.id, planPriceId);

      await startPayment({
        request_key: requestKey,
        payment_gateway: 'VNPAY',
        plan_price_id: planPriceId,
        curr_code: 'VND',
        locale: 'vn',
        add_data: 'some_additional_data',
      });
    } catch (error) {
      console.error('Payment initialization failed:', error);
    }
  };

  // Show VNPay POST form if payment data with VNPay details is available
  if (paymentData?.ispTxnId) {
    return (
      <VNPayPostForm
        action="https://sandbox.vnpayment.vn/isp-svc/recurring-payment/pay"
        ispTxnId={paymentData.ispTxnId}
        tmnCode={paymentData.tmnCode!}
        dataKey={paymentData.dataKey!}
      />
    );
  }

  return (
    <div className={`relative mb-8 flex flex-col pt-10 ${isDisabled ? 'pointer-events-none opacity-50' : ''}`}>
      <Typography variant="headlineSm" className="mb-2">
        Chọn phương thức thanh toán
      </Typography>
      <Typography variant="bodySm" className="mb-6 text-neutral-500">
        Secure payment via VNPay gateway
      </Typography>

      <RadioGroup
        value={selectedPaymentMethod}
        onChange={(e) => onPaymentMethodChange(e.target.value)}
        className="space-y-4"
      >
        {/* VNPAY Option */}
        <div
          className={`relative w-full cursor-pointer rounded-xl border-2 p-4 transition-all ${
            selectedPaymentMethod === 'vnpay'
              ? 'border-blue-500 bg-blue-50'
              : 'border-neutral-200 bg-white hover:border-neutral-300'
          }`}
          onClick={() => onPaymentMethodChange('vnpay')}
        >
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <Radio value="vnpay" className="shrink-0" />
              <div className="ml-3">
                <Typography variant="bodyMd" className="text-neutral-900">
                  Thanh toán trực tuyến VNPAY
                </Typography>
              </div>
            </div>
            <div className="flex items-center">
              <img src="/images/payment/vnpay.png" alt="VNPAY" className="size-12" />
            </div>
          </div>
        </div>

        {/* Confirm Payment Button */}
        <Button
          className="w-full"
          onClick={handleConfirmPayment}
          disabled={!planPriceId || !userInfo?.info?.id || isPaymentLoading}
        >
          <div className="flex items-center justify-center">
            <Typography
              variant="bodyMd"
              className={planPriceId && userInfo?.info?.id ? 'text-white' : 'text-neutral-600'}
            >
              {isPaymentLoading ? 'Đang xử lý...' : 'Xác nhận thanh toán'}
            </Typography>
          </div>
        </Button>
      </RadioGroup>
    </div>
  );
};

export default PaymentMethodSelection;
