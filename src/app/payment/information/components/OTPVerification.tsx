'use client';

import Button from '@/components/ui/button/button';
import Typography from '@/components/ui/typography/typography';

interface OTPVerificationProps {
  email: string;
  otp: string[];
  error: string;
  isVerifying: boolean;
  isResending: boolean;
  onOtpChange: (index: number, value: string) => void;
  onOtpKeyDown: (index: number, e: React.KeyboardEvent) => void;
  onVerify: () => void;
  onResend: () => void;
  onChangeEmail: () => void;
  onOtpArrayChange?: (newOtp: string[]) => void;
}

const OTPVerification = ({
  email,
  otp,
  error,
  isVerifying,
  isResending,
  onOtpChange,
  onOtpKeyDown,
  onVerify,
  onResend,
  onChangeEmail,
  onOtpArrayChange,
}: OTPVerificationProps) => {
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (!otp.some((digit) => !digit) && !isVerifying) {
      onVerify();
    }
  };

  const handleOtpKeyDown = (index: number, e: React.KeyboardEvent) => {
    // Handle Enter key submission
    if (e.key === 'Enter') {
      e.preventDefault();
      if (!otp.some((digit) => !digit) && !isVerifying) {
        onVerify();
      }
    } else {
      onOtpKeyDown(index, e);
    }
  };

  const handlePaste = (e: React.ClipboardEvent) => {
    e.preventDefault();
    const pastedData = e.clipboardData.getData('text');

    // Remove any non-numeric characters and limit to 6 digits
    const numericData = pastedData.replace(/\D/g, '').slice(0, 6);

    if (numericData.length > 0) {
      // Create new OTP array
      const newOtp = ['', '', '', '', '', ''];

      // Fill with pasted digits
      for (let i = 0; i < numericData.length && i < 6; i++) {
        newOtp[i] = numericData[i];
      }

      // If we have the array change handler, use it (more efficient)
      if (onOtpArrayChange) {
        onOtpArrayChange(newOtp);
      } else {
        // Fallback to individual updates
        newOtp.forEach((digit, index) => {
          onOtpChange(index, digit);
        });
      }

      // Focus the appropriate field
      setTimeout(() => {
        const focusIndex = Math.min(numericData.length, 5);
        const targetInput = document.getElementById(`otp-${focusIndex}`);
        targetInput?.focus();
      }, 10);
    }
  };

  return (
    <div className="mb-8 flex flex-col">
      <Typography variant="headlineMd" className="text-gray-900 mb-4">
        Nhập thông tin tài khoản
      </Typography>

      <div className="flex flex-col gap-2">
        <Typography variant="bodyLg" className="flex flex-col gap-2">
          Welcome back! Please enter the 6-digit code we sent to this email:{' '}
        </Typography>
        <div className="flex gap-2">
          <Typography variant="labelLg">{email}</Typography>{' '}
          <button onClick={onChangeEmail} className="cursor-pointer">
            <Typography variant="labelMd" className="text-primary-500">
              Đổi email
            </Typography>
          </button>
        </div>
      </div>

      <form onSubmit={handleSubmit} className="mt-5">
        {/* OTP Input Fields */}
        <div className="mb-6">
          <div className="mb-4 flex gap-3">
            {otp.map((digit, index) => (
              <input
                key={index}
                id={`otp-${index}`}
                type="text"
                value={digit}
                onChange={(e) => onOtpChange(index, e.target.value)}
                onKeyDown={(e) => handleOtpKeyDown(index, e)}
                onPaste={handlePaste}
                className={`h-12 w-12 rounded-lg border text-center text-lg font-medium focus:border-transparent focus:outline-none focus:ring-2 ${
                  error ? 'border-red-500 focus:ring-red-500' : 'border-gray-300 focus:ring-blue-500'
                }`}
                maxLength={1}
                pattern="[0-9]"
                inputMode="numeric"
                autoComplete="one-time-code"
              />
            ))}
          </div>
          {error && (
            <Typography variant="bodySm" className="text-center text-red-500">
              {error}
            </Typography>
          )}
        </div>

        {/* Resend Code */}
        <div className="mb-8">
          <button
            type="button"
            onClick={onResend}
            className="cursor-pointer text-sm text-blue-600 underline"
            disabled={isResending}
          >
            {isResending ? 'Đang gửi lại...' : 'Resend code'}
          </button>
        </div>

        {/* Continue Button */}
        <Button
          htmlType="submit"
          variant="primary"
          className="mb-8 h-[48px] w-full"
          loading={isVerifying}
          disabled={otp.some((digit) => !digit) || isVerifying}
        >
          {isVerifying ? 'Đang xác thực...' : 'Tiếp tục thanh toán'}
        </Button>
      </form>
    </div>
  );
};

export default OTPVerification;
