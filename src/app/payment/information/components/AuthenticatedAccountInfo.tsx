'use client';

import Typography from '@/components/ui/typography/typography';
import { CheckCircleOutlined } from '@ant-design/icons';

interface AuthenticatedAccountInfoProps {
  email: string;
  onChangeEmail: () => void;
}

const AuthenticatedAccountInfo = ({ email, onChangeEmail }: AuthenticatedAccountInfoProps) => {
  return (
    <div className="border-b border-neutral-100 pb-10">
      <Typography variant="headlineSm" className="mb-6">
        Thông tin tài khoản
      </Typography>

      <div className="flex items-center justify-between rounded-lg">
        <div className="flex items-center space-x-2">
          <Typography variant="labelLg">{email}</Typography>
          <CheckCircleOutlined className="text-lg text-green-500" />
        </div>

        <button onClick={onChangeEmail}>
          <Typography variant="labelMd" className="text-primary-500">
            Đ<PERSON>i email
          </Typography>
        </button>
      </div>
    </div>
  );
};

export default AuthenticatedAccountInfo;
