'use client';

import Typography from '@/components/ui/typography/typography';

interface OrderSummaryData {
  planName: string;
  planDiscount: string;
  basePrice: number;
  taxRate: number;
  taxAmount: number;
  totalAmount: number;
}

interface OrderSummaryProps {
  orderSummary: OrderSummaryData;
}

const OrderSummary = ({ orderSummary }: OrderSummaryProps) => {
  return (
    <div className="bg-white md:col-span-4">
      <div className="ml-20 mt-40 max-w-[364px]">
        <div className="rounded-xl bg-white p-6">
          <div className="border-b border-neutral-100 pb-3">
            <Typography variant="labelMd">CHI TIẾT ĐƠN HÀNG</Typography>
          </div>

          <div className="space-y-4 pt-3">
            <div className="flex justify-between">
              <div>
                <Typography variant="bodyMd" className="text-gray-900">
                  {orderSummary.planName}
                </Typography>
                {orderSummary.planDiscount && (
                  <Typography variant="bodySm" className="text-gray-500">
                    {orderSummary.planDiscount}
                  </Typography>
                )}
              </div>
              <Typography variant="bodyMd" className="text-gray-900">
                {orderSummary.basePrice.toLocaleString('vi-VN')}đ
              </Typography>
            </div>

            <div className="flex justify-between">
              <Typography variant="bodyMd" className="text-gray-900">
                VAT ({orderSummary.taxRate}%)
              </Typography>
              <Typography variant="bodyMd" className="text-gray-900">
                {orderSummary.taxAmount.toLocaleString('vi-VN')}đ
              </Typography>
            </div>

            <div className="border-t border-neutral-100 pt-4">
              <div className="flex items-center justify-between">
                <Typography variant="titleMd" className="text-gray-900">
                  Tổng thanh toán
                </Typography>
                <Typography variant="titleLg" className="text-gray-900">
                  {orderSummary.totalAmount.toLocaleString('vi-VN')}đ
                </Typography>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default OrderSummary;
