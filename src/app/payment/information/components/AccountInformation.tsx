'use client';

import Button from '@/components/ui/button/button';
import Typography from '@/components/ui/typography/typography';

interface AccountInformationProps {
  email: string;
  onEmailChange: (email: string) => void;
  onContinue: () => void;
  error: string;
  isLoading: boolean;
}

const AccountInformation = ({ email, onEmailChange, onContinue, error, isLoading }: AccountInformationProps) => {
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (email && !isLoading) {
      onContinue();
    }
  };

  return (
    <div className="mb-8 flex flex-col">
      <Typography variant="headlineMd" className="text-gray-900 mb-4">
        Nhập thông tin tài khoản
      </Typography>

      <Typography variant="bodyLg" className="mb-6">
        <strong>A Studify account</strong> is required to access your subscription benefits. Please make sure your email
        address is accurate, as we'll use it to send your subscription confirmation and important updates. By
        subscribing, you agree to our <span className="cursor-pointer text-blue-600 underline">Terms of Use</span> and{' '}
        <span className="cursor-pointer text-blue-600 underline">Privacy Policy</span>.
      </Typography>

      <form onSubmit={handleSubmit}>
        <div className="mb-2">
          <input
            type="email"
            value={email}
            onChange={(e) => onEmailChange(e.target.value)}
            className={`w-full rounded-lg border px-4 py-3 focus:border-transparent focus:outline-none focus:ring-2 ${
              error ? 'border-red-500 focus:ring-red-500' : 'border-gray-300 focus:ring-blue-500'
            }`}
            placeholder="Enter your email address"
            autoComplete="email"
          />
          {error && (
            <Typography variant="bodySm" className="mt-2 text-red-500">
              {error}
            </Typography>
          )}
        </div>

        <div className="mb-2">
          <Typography variant="titleMd">Không yêu cầu mật khẩu</Typography>
        </div>

        <Button
          htmlType="submit"
          variant="primary"
          className="h-[48px] w-full"
          loading={isLoading}
          disabled={!email || isLoading}
        >
          {isLoading ? 'Đang gửi...' : 'Tiếp tục'}
        </Button>
      </form>
    </div>
  );
};

export default AccountInformation;
