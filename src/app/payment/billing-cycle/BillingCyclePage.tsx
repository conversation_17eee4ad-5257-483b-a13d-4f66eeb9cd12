'use client';

import Button from '@/components/ui/button/button';
import Radio from '@/components/ui/radio/radio';
import RadioGroup from '@/components/ui/radio/radio-group';
import Steps from '@/components/ui/steps/steps';
import Typography from '@/components/ui/typography/typography';
import { useGetPlans, useGetTaxRate } from '@/hooks/apis/payment';
import { ArrowLeftOutlined } from '@ant-design/icons';
import { useRouter } from 'next/navigation';
import React, { useMemo, useState } from 'react';

function BillingCyclePage() {
  const [selectedPlan, setSelectedPlan] = useState<string>('');
  const router = useRouter();

  const { data: plansData, isLoading: isLoadingPlans } = useGetPlans();
  const { data: taxRateData, isLoading: isLoadingTaxRate } = useGetTaxRate();

  const stepItems = [
    {
      title: 'Thông tin gói đăng ký',
      status: 'process' as const,
    },
    {
      title: 'Thông tin thanh toán',
      status: 'wait' as const,
    },
  ];

  // Process plans data from API
  const plans = useMemo(() => {
    if (!plansData || plansData.length === 0) return [];

    const plan = plansData[0]; // Assuming we get the "Pro Plan" from API
    if (!plan?.planPrices) return [];

    // Sort by billing cycle (12=yearly, 3=quarterly, 1=monthly)
    const sortedPrices = [...plan.planPrices].sort((a, b) => b.billingCycle - a.billingCycle);

    return sortedPrices.map((planPrice) => {
      const isYearly = planPrice.billingCycle === 12;
      const isQuarterly = planPrice.billingCycle === 3;
      const isMonthly = planPrice.billingCycle === 1;

      // Calculate monthly price and total price
      const monthlyPrice = planPrice.basePrice; // basePrice is already the monthly price
      const totalPrice = planPrice.basePrice * planPrice.billingCycle; // Total for the entire billing cycle

      // Calculate discount compared to monthly price
      const monthlyPlanPrice = sortedPrices.find((p) => p.billingCycle === 1)?.basePrice || planPrice.basePrice;
      const monthlyBasePrice = monthlyPlanPrice;
      const discount = monthlyBasePrice > 0 ? Math.round((1 - monthlyPrice / monthlyBasePrice) * 100) : 0;

      return {
        id: planPrice.id,
        title: isYearly ? 'Hàng năm' : isQuarterly ? 'Hàng quý' : 'Hàng tháng',
        price: `${monthlyPrice.toLocaleString('vi-VN')}đ/tháng`,
        totalPrice: planPrice.billingCycle > 1 ? `Thanh toán 1 lần: ${totalPrice.toLocaleString('vi-VN')}đ` : '',
        discount: discount > 0 ? `Tiết kiệm ${discount}%` : '',
        popular: isYearly,
        bgColor: isYearly ? '#2E2EE5' : 'white',
        borderColor: isYearly ? undefined : '#D4DAE5',
        textColor: isYearly ? 'white' : 'black',
        billingCycle: planPrice.billingCycle,
        basePrice: planPrice.basePrice,
        grandTotal: totalPrice,
      };
    });
  }, [plansData]);

  // Set initial selected plan when plans are loaded
  React.useEffect(() => {
    if (plans.length > 0 && !selectedPlan) {
      // Default to yearly plan
      const yearlyPlan = plans.find((p) => p.billingCycle === 12);
      if (yearlyPlan) {
        setSelectedPlan(yearlyPlan.id);
      } else {
        setSelectedPlan(plans[0].id);
      }
    }
  }, [plans, selectedPlan]);

  // Calculate order summary based on selected plan
  const orderSummary = useMemo(() => {
    const currentPlan = plans.find((p) => p.id === selectedPlan) as any;

    if (!currentPlan || !taxRateData) {
      return {
        planName: '',
        planDiscount: '',
        basePrice: 0,
        taxRate: 0,
        taxAmount: 0,
        totalAmount: 0,
      };
    }
    const basePrice = currentPlan.grandTotal; // Use total price for the entire billing cycle
    const taxRate = taxRateData.rate; // Rate is already a percentage (e.g., 8)
    const taxAmount = Math.round(basePrice * (taxRateData.rate / 100)); // Convert percentage to decimal for calculation
    const totalAmount = basePrice + taxAmount;

    return {
      planName: `Thành toán ${currentPlan.title.toLowerCase()}`,
      planDiscount: currentPlan.discount ? `(${currentPlan.discount})` : '',
      basePrice,
      taxRate,
      taxAmount,
      totalAmount,
    };
  }, [plans, selectedPlan, taxRateData]);

  return (
    <div className="bg-gray-50 min-h-screen bg-neutral-50">
      <div className="grid min-h-screen grid-cols-1 gap-8 md:grid-cols-10">
        <div className="ml-auto mt-8 pt-24 md:col-span-6">
          <div className="">
            <div className="mb-8 flex items-center">
              <button
                className="mr-4 flex h-10 w-10 items-center justify-center rounded-lg border border-neutral-200 bg-white"
                onClick={() => router.push('/profile/setting')}
              >
                <ArrowLeftOutlined className="text-gray-600 text-xl" />
              </button>
            </div>

            {/* Steps */}
            <div className="mb-8 w-[429px]">
              <Steps current={0} items={stepItems} />
            </div>

            {/* Plan Selection */}
            <div className="mb-8">
              <Typography variant="headlineMd" className="text-gray-900 mb-6">
                Chọn chu kỳ thanh toán
              </Typography>

              {isLoadingPlans ? (
                <div className="space-y-4">
                  {[1, 2, 3].map((i) => (
                    <div key={i} className="border-gray-200 bg-gray-50 animate-pulse rounded-xl border-2 p-6">
                      <div className="bg-gray-200 h-6 w-1/3 rounded"></div>
                    </div>
                  ))}
                </div>
              ) : (
                <RadioGroup
                  value={selectedPlan}
                  onChange={(e) => setSelectedPlan(e.target.value)}
                  className="w-full space-y-4"
                >
                  {plans.map((plan) => (
                    <div
                      key={plan.id}
                      className={`relative w-full cursor-pointer rounded-xl p-6 ${
                        selectedPlan === plan.id
                          ? `border-2 border-primary-500 bg-primary-50`
                          : 'border-2 border-transparent hover:border-gray-300 bg-white shadow-sm'
                      }`}
                      onClick={() => setSelectedPlan(plan.id)}
                    >
                      <div className="flex w-full items-center">
                        <Radio value={plan.id} className="shrink-0" />
                        <div className="ml-3 flex flex-1 items-center justify-between">
                          <div className="flex items-center gap-3">
                            <Typography variant="titleMd" className="text-gray-900">
                              {plan.title}
                            </Typography>
                            {plan.discount && (
                              <span
                                className="rounded-full px-3 py-1 text-sm font-medium"
                                style={{
                                  backgroundColor: plan.bgColor,
                                  border: `1px solid ${plan.borderColor}`,
                                  color: plan.textColor,
                                }}
                              >
                                {plan.discount}
                              </span>
                            )}
                          </div>
                          <div className="flex flex-col text-right">
                            <Typography variant="titleLg" className="text-gray-900">
                              {plan.price}
                            </Typography>
                            {plan.totalPrice && (
                              <Typography variant="bodySm" className="text-gray-600">
                                {plan.totalPrice}
                              </Typography>
                            )}
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </RadioGroup>
              )}
            </div>

            {/* Continue Button */}
            <Button
              variant="primary"
              className="h-[48px] w-full"
              onClick={() => router.push(`/payment/information?plan=${selectedPlan}`)}
            >
              Tiếp theo: Thông tin thanh toán
            </Button>

            {/* Package Info */}
            <div className="mt-8">
              <Typography variant="headlineSm" className="text-gray-900">
                Gói đăng ký
              </Typography>

              <img src="/images/payment/select_plan.png" className="mt-6" alt="package-info" width="100%" />
            </div>
          </div>
        </div>

        <div className="bg-white md:col-span-4">
          <div className="ml-20 mt-40 max-w-[364px]">
            <div className="rounded-xl bg-white p-6">
              <div className="border-b border-neutral-100 pb-3">
                <Typography variant="labelMd">CHI TIẾT ĐƠN HÀNG</Typography>
              </div>

              <div className="space-y-4 pt-3">
                <div className="flex justify-between">
                  <div>
                    <Typography variant="bodyMd" className="text-gray-900">
                      {orderSummary.planName}
                    </Typography>
                    {orderSummary.planDiscount && (
                      <Typography variant="bodySm" className="text-gray-500">
                        {orderSummary.planDiscount}
                      </Typography>
                    )}
                  </div>
                  <Typography variant="bodyMd" className="text-gray-900">
                    {orderSummary.basePrice.toLocaleString('vi-VN')}đ
                  </Typography>
                </div>

                <div className="flex justify-between">
                  <Typography variant="bodyMd" className="text-gray-900">
                    VAT ({orderSummary.taxRate}%)
                  </Typography>
                  <Typography variant="bodyMd" className="text-gray-900">
                    {orderSummary.taxAmount.toLocaleString('vi-VN')}đ
                  </Typography>
                </div>

                <div className="border-t border-neutral-100 pt-4">
                  <div className="flex items-center justify-between">
                    <Typography variant="titleMd" className="text-gray-900">
                      Tổng thanh toán
                    </Typography>
                    <Typography variant="titleLg" className="text-gray-900">
                      {orderSummary.totalAmount.toLocaleString('vi-VN')}đ
                    </Typography>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default BillingCyclePage;
