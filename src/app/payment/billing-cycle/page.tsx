import { userServices } from '@/services/user.services';
import { redirect } from 'next/navigation';
import BillingCyclePage from './BillingCyclePage';

async function Page() {
  const { hasSubscription } = await userServices();
  // Redirect to home if user already has an active subscription
  if (hasSubscription) {
    redirect('/');
  }

  return (
    <div>
      <BillingCyclePage />
    </div>
  );
}

export default Page;
