import { HttpStatusCode } from '@/constants/api';
import { handleAsync } from '@/lib/handle-async';
import { LearnCoursePage } from '@/modules/courses';
import { getCourseByIdService, getLessonById } from '@/modules/courses/services/course.service';
import { getTestByIdService } from '@/modules/courses/services/test.service';
import { AppProps } from 'type/appProps';
import PaymentDetector from './PaymentDetector';

export default async function LearnLecturePage(props: Readonly<AppProps>) {
  const params = await props.params;
  const { courseId, sectionId, lectureId } = params;

  const [_, courseData] = await handleAsync(getCourseByIdService(courseId));

  const [lectureDetailError, lectureDetail] = await handleAsync(getLessonById({ courseId, sectionId, lectureId }));

  if (lectureDetailError?.status === HttpStatusCode.FORBIDDEN) {
    return <PaymentDetector />;
  }

  const testDetail = lectureDetail?.test?.id
    ? await getTestByIdService({ courseId, sectionId, testId: lectureDetail?.test?.id || '' })
    : null;

  if (!courseData || !lectureDetail) {
    return <div>Không tìm thấy khóa học</div>;
  }

  return <LearnCoursePage testDetail={testDetail} courseInfo={courseData} lectureInfo={lectureDetail} />;
}
