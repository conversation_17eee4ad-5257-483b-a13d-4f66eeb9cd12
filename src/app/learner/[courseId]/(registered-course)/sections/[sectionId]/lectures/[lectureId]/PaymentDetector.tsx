'use client';

import { Button, Typography } from '@/components/ui';
import { routePaths } from '@/config';
import { formatApiUrl } from '@/utils/url.util';
import { useParams, useRouter } from 'next/navigation';

const PaymentDetector = () => {
  const router = useRouter();

  const params = useParams<{ courseId: string }>();

  const handleBackToCourseDetailPage = () => {
    router.replace(formatApiUrl(routePaths.profile.children.course.children.detail.path, { id: params.courseId }));
  };

  return (
    <div className="flex h-screen w-full flex-col items-center justify-center gap-4">
      <Typography variant="titleLg">Tài khoản chưa được nâng cấp</Typography>

      <Typography variant="bodyMd">
        Bạn cần nâng cấp tài khoản để học bài giảng này. Vui lòng nâng cấp tài khoản để tiếp tục học tập.
      </Typography>

      <div>
        <Button onClick={handleBackToCourseDetailPage}>Quay lại trang khóa học chi tiết</Button>
      </div>
    </div>
  );
};

export default PaymentDetector;
