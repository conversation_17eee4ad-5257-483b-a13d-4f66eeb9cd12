//overwrite
@import '../styles/overwrite.scss';
@import '../styles/common.scss';

//fonts
@import '../styles/fonts.scss';
// @import url('https://fonts.googleapis.com/css2?family=Be+Vietnam+Pro:wght@300;400;500;600;700&display=swap');
// @import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600&display=swap');
// @import url('https://fonts.googleapis.com/css2?family=Roboto:wght@400;500;700&display=swap');
// @import url('https://fonts.googleapis.com/css2?family=Noto+Sans:wght@400;500;600;700&display=swap');
// @import url('https://fonts.googleapis.com/css2?family=Plus+Jakarta+Sans:wght@300;400;500;600;700&display=swap');
// @import url('https://fonts.googleapis.com/css2?family=Roboto+Slab:wght@300;400;500;600&display=swap');
// @import url('https://fonts.googleapis.com/css2?family=Noto+Serif:wght@400;700&display=swap');
// @import url('https://fonts.googleapis.com/css2?family=Playfair+Display:wght@400;500;600;700&display=swap');
// @import url('https://fonts.googleapis.com/css2?family=Poppins:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,500;1,600;1,700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Nunito:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,500;1,600;1,700&display=swap');

.content-container {
  height: calc(100vh - theme('spacing.7'));
}

.bg-my-course {
  background:
    linear-gradient(180deg, rgba(9, 2, 44, 0) 0%, #09022b 100%),
    linear-gradient(270deg, rgba(9, 2, 44, 0) 0%, #09022b 100%);
  padding: 24px;
}

.line-clamp-2 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
}

.line-clamp-4 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 4;
}

.line-clamp-3 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3;
}

.line-clamp-1 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
}

.swiper-button-disabled {
  display: none !important;
}

.triangle-top-left {
  width: 0;
  height: 0;
  border-top: 44px solid #e5e5e9;
  border-right: 44px solid transparent;
}

.input-chat {
  -moz-appearance: textfield;
  -webkit-appearance: textfield;
  //background-color: white;
  background-color: -moz-field;
  //border: 1px solid darkgray;
  //box-shadow: 1px 1px 1px 0 lightgray inset;
  padding: 12px 8px;
  width: 100%;
  min-height: 48px;

  &:focus-visible {
    outline: none;
  }
}

.input-chat[placeholder]:empty:before {
  content: attr(placeholder);
  color: #555;
}

.input-chat[placeholder]:empty:focus:before {
  content: '';
}

.master-class-swiper-custom {
  @media screen and (max-width: 768px) {
    display: none;
  }

  .swiper-pagination-bullet {
    background: #7677fd;
  }

  .swiper-pagination-bullet-active {
    background: white;
    width: 24px;
    height: 8px;
    border-radius: 8px;
  }
}

button.antd-switch-custom {
  background: rgba(0, 0, 0, 0.45);
}

button.antd-switch-custom {
  background: rgba(0, 0, 0, 0.45);
}

.tox-tbtn[aria-label='Delete table'],
div[title='Delete table'][aria-disabled='false'] {
  display: none !important;
}

.position-center {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.font-bold {
  font-weight: bold;
}

.btn-custom {
  display: inline-block;
  font-size: 16px;
  padding: 12px 24px;
  background-color: #1b1dfb;
  color: white;
  border-radius: 8px;
  border: 1px solid transparent;

  &:hover {
    background-color: white;
    color: #1b1dfb;
    border: 1px solid #1b1dfb;
  }
}

.btn-custom-outline {
  display: inline-block;
  font-size: 16px;
  padding: 12px 24px;
  background-color: white;
  color: #1b1dfb;
  border-radius: 8px;
  border: 1px solid #1b1dfb;

  &:hover {
    background-color: #1b1dfb;
    color: white;
  }
}

.ant-collapse-header-text {
  display: block;
  max-width: calc(100% - 20px);
}

.tox-editor-container {
  z-index: 999;
}
