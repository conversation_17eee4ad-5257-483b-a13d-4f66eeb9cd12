'use server';

import { routePaths } from '@/config';
import { COOKIE_NAMES } from '@/constants/auth';
import { NextRequest, NextResponse } from 'next/server';

const options = { maxAge: 0, domain: '.studify.vn', httpOnly: true, path: '/' };

export async function GET(request: NextRequest) {
  const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'https://beta.studify.vn';

  const adminBaseUrl = process.env.NEXT_PUBLIC_ADMIN_BASE_URL;

  if (!process.env.NEXT_PUBLIC_BASE_URL) {
    console.warn('NEXT_PUBLIC_BASE_URL is not set, using fallback URL');
  }

  const nextUrl = request.headers.get('next-url') || '/';

  const refererUrl = request.headers.get('referer')?.replace(baseUrl, '');

  const redirectUrl = refererUrl || nextUrl;

  const isAdminPreview =
    request.nextUrl.pathname.startsWith('/admin-preview') || request.nextUrl.pathname.includes('/admin-preview');

  if (isAdminPreview && adminBaseUrl) {
    const loginAdminUrl = `${adminBaseUrl}/login/?redirectUrl=${encodeURIComponent(redirectUrl)}`;

    const response = NextResponse.redirect(loginAdminUrl, { status: 302 });

    response.cookies.set(COOKIE_NAMES.ADMIN_ACCESS_TOKEN, '', options);
    response.cookies.set(COOKIE_NAMES.ADMIN_REFRESH_TOKEN, '', options);
    response.cookies.set(COOKIE_NAMES.ADMIN_INFO, '', options);

    return response;
  }

  const loginUrl = `${baseUrl}${routePaths.login}?redirectUrl=${encodeURIComponent(redirectUrl)}`;

  const response = NextResponse.redirect(loginUrl, { status: 302 });

  response.cookies.set(COOKIE_NAMES.ACCESS_TOKEN, '', options);
  response.cookies.set(COOKIE_NAMES.REFRESH_TOKEN, '', options);
  response.cookies.set(COOKIE_NAMES.USER_INFO, '', options);

  return response;
}
