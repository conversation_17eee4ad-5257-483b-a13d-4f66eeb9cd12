import { getCoursesOfCreator, getPublicCoursesOfCreator } from '@/features/courses/services/server';
import { AppProps } from '@/type/appProps';
import CreatorPage from 'components/creator-page/CreatorPage';
import Footer from 'components/Footer';
import HeaderGuest from 'components/header/HeaderGuest';
import <PERSON><PERSON><PERSON><PERSON> from 'components/header/HeaderLogged';
import { userServices } from 'services/user.services';

// const ALL_TOPIC = '0';

// export async function generateMetadata(props: AppProps, parent: ResolvingMetadata): Promise<Metadata> {
//   const courses = await courseServices().getPublishedCourses({
//     ...defaultGetCourse,
//     key_search: '',
//     topic_id: ALL_TOPIC,
//     user_id: props.params.id,
//   });
//   if (courses.list.length <= 0)
//     return {
//       title: 'Không có khóa học nào',
//     };
//   const { name, short_introduce, introduce, avatar } = courses.list[0]?.user ?? {};
//   return {
//     title: name,
//     description: short_introduce ?? introduce,
//     openGraph: {
//       images: [
//         {
//           url: avatar ?? '',
//         },
//       ],
//     },
//   };
// }

async function CourseDetailPage(props: AppProps) {
  const params = await props.params;
  const { isLoggedIn } = await userServices();

  const coursesOfCreator = isLoggedIn
    ? await getCoursesOfCreator({ userId: params.id })
    : await getPublicCoursesOfCreator({ userId: params.id });

  return (
    <>
      <div className={'mx-auto max-w-7xl overflow-x-hidden'}>
        <div className={'border-b-1 border-#EBEBFF m-4 flex justify-between gap-8 rounded-xl bg-white p-4 lg:m-auto'}>
          {isLoggedIn ? <HeaderLogged /> : <HeaderGuest />}
        </div>
        <div className={'center-x-y'}>
          <CreatorPage courses={coursesOfCreator} isLoggedIn={isLoggedIn} />
        </div>
      </div>
      <Footer />
    </>
  );
}

export default CourseDetailPage;
