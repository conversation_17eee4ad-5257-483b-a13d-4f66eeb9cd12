import React from 'react';
import HeaderGuest from 'components/header/HeaderGuest';
import HeaderLogged from 'components/header/HeaderLogged';
import CreatorBanner from 'components/creator/Banner';
import CreatorReason from 'components/creator/Reason';
import { userServices } from 'services/user.services';
import { Metadata } from 'next';
import Footer from 'components/Footer';
import { BecomeCreator } from 'components/creator/BecomeCreator';
import CreatorHighlight from 'components/home/<USER>';
import { creatorService } from 'services/creator.service';

export const metadata: Metadata = {
  title: 'Giảng dạy với Studify',
  description: 'Trở thành một người hướng dẫn và thay đổi cuộc sống nhiều người, bao gồm cả chính bạn',
  openGraph: {
    title: 'Giảng dạy với Studify',
    description: 'Trở thành một người hướng dẫn và thay đổi cuộc sống nhiều người, bao gồm cả ch<PERSON>h bạn',
    images: [
      {
        url: '/images/bg_creator.png',
      },
    ],
  },
};

async function CreatorPage() {
  const { isLoggedIn } = await userServices();

  let header = <HeaderGuest />;

  if (isLoggedIn) {
    header = <HeaderLogged />;
  }

  const creators = await creatorService().highlightCreators();

  return (
    <div
      // id="about-us"
      className={'pt-6 overflow-x-hidden'}
    >
      <CreatorBanner header={header} />
      <div className={'pt-8'}>
        <CreatorReason
          becomeCreator={
            <BecomeCreator creatorHighlight={<CreatorHighlight prominentAuthors={creators} className={'bg-white'} />} />
          }
        />
      </div>
      <Footer />
    </div>
  );
}

export default CreatorPage;
