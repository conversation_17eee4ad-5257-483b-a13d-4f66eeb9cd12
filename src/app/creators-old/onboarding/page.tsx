import Onboarding from 'components/onboarding/Onboarding';
import { routePaths } from 'config/constant';
import { redirect } from 'next/navigation';
import { userServices } from 'services/user.services';

// DEPRECATED: Will confirm to remove this

async function CourseOnboardingPage() {
  const { isOnboardedCreator } = await userServices();

  if (isOnboardedCreator) {
    redirect(routePaths.createCourse);
  }

  return <Onboarding />;
}

export default CourseOnboardingPage;
