import { handleAsync } from '@/lib/handle-async';
import AdminPreviewCoursePage from '@/modules/courses/features/learn-course/pages/AdminPreviewCoursePage';
import { AdminPreviewServices } from '@/modules/courses/services/admin.service';
import { AppProps } from 'type/appProps';

export default async function AdminPreviewCourse(props: Readonly<AppProps>) {
  const params = await props.params;
  const { courseId } = params;

  const [, courseData] = await handleAsync(AdminPreviewServices.getCourse({ courseId }));

  if (!courseData) return <div><PERSON>hông tìm thấy khóa học</div>;

  const firstSection = courseData?.sections?.[0];
  const firstLecture = firstSection?.lectures?.[0];
  const sectionId = firstSection?.id || '';
  const lectureId = firstLecture?.id || '';

  if (!sectionId || !lectureId) return <div><PERSON><PERSON>ông tìm thấy bài học</div>;

  const [_, lectureDetail] = await handleAsync(AdminPreviewServices.getLecture({ courseId, sectionId, lectureId }));

  const testDetail = lectureDetail?.test?.id
    ? await AdminPreviewServices.getTest({ courseId, sectionId, testId: lectureDetail?.test?.id || '' })
    : null;

  return (
    <AdminPreviewCoursePage
      sectionId={sectionId}
      lectureId={lectureId}
      isAdminPreview
      testDetail={testDetail}
      courseInfo={courseData}
      lectureInfo={lectureDetail}
    />
  );
}
