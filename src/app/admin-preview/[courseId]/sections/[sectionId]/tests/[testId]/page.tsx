import { handleAsync } from '@/lib/handle-async';
import AdminPreviewCoursePage from '@/modules/courses/features/learn-course/pages/AdminPreviewCoursePage';
import { AdminPreviewServices } from '@/modules/courses/services/admin.service';
import { AppProps } from 'type/appProps';

export default async function LearnLecturePage(props: Readonly<AppProps>) {
  const params = await props.params;
  const { courseId, sectionId, testId = '' } = params;

  const [_, courseData] = await handleAsync(AdminPreviewServices.getCourse({ courseId }));

  const [, testDetail] = await handleAsync(AdminPreviewServices.getTest({ courseId, sectionId, testId }));

  if (!courseData || !testDetail) {
    return <div>Không tìm thấy bài kiểm tra</div>;
  }

  return (
    <AdminPreviewCoursePage
      isAdminPreview
      sectionId={sectionId}
      lectureId={testId}
      testDetail={testDetail}
      courseInfo={courseData}
    />
  );
}
