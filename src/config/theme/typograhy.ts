export const FONT_SVN_POPPINS = 'SVN-Poppins';
export const FONT_NUNITO = 'Nunito';

export const typographyThemeConfig = {
  // Display
  displayLg: {
    fontSize: '58px',
    lineHeight: '64px',
    fontWeight: 700,
    letterSpacing: '-0.25px',
  },
  displayMd: {
    fontSize: '45px',
    lineHeight: '52px',
    fontWeight: 700,
    letterSpacing: '-0.25px',
  },
  displaySm: {
    fontSize: '36px',
    lineHeight: '44px',
    fontWeight: 700,
    letterSpacing: '-0.25px',
  },

  // Headline
  headlineLg: {
    fontSize: '32px',
    lineHeight: '40px',
    fontWeight: 600,
    letterSpacing: '-0.1px',
  },
  headlineMd: {
    fontSize: '28px',
    lineHeight: '40px',
    fontWeight: 600,
    letterSpacing: '-0.1px',
  },
  headlineSm: {
    fontSize: '24px',
    lineHeight: '40px',
    fontWeight: 600,
    letterSpacing: '-0.1px',
  },
  headlineXs: {
    fontSize: '20px',
    lineHeight: '28px',
    fontWeight: 600,
    letterSpacing: '0px',
  },

  // Title
  titleLg: {
    fontSize: '22px',
    lineHeight: '40px',
    fontWeight: 700,
    letterSpacing: '0px',
  },
  titleMd: {
    fontSize: '16px',
    lineHeight: '24px',
    fontWeight: 700,
    letterSpacing: '0.1px',
  },
  titleSm: {
    fontSize: '14px',
    lineHeight: '20px',
    fontWeight: 700,
    letterSpacing: '0.15px',
  },

  // Label
  labelLg: {
    fontSize: '16px',
    lineHeight: '24px',
    fontWeight: 500,
    letterSpacing: '0px',
  },
  labelMd: {
    fontSize: '14px',
    lineHeight: '20px',
    fontWeight: 500,
    letterSpacing: '0.1px',
  },
  labelSm: {
    fontSize: '12px',
    lineHeight: '16px',
    fontWeight: 500,
    letterSpacing: '0.25px',
  },
  labelXs: {
    fontSize: '11px',
    lineHeight: '20px',
    fontWeight: 500,
    letterSpacing: '0.25px',
  },

  // Body
  bodyLg: {
    fontSize: '16px',
    lineHeight: '24px',
    fontWeight: 500,
    letterSpacing: '0px',
  },
  bodyMd: {
    fontSize: '14px',
    lineHeight: '20px',
    fontWeight: 500,
    letterSpacing: '0.1px',
  },
  bodySm: {
    fontSize: '12px',
    lineHeight: '16px',
    fontWeight: 500,
    letterSpacing: '0.1px',
  },
};
