import { API_ENDPOINTS } from '@/constants/api';
import { fetcher } from '@/lib/fetcher';

// Types based on the API response structure from the image
export interface PlanPrice {
  id: string;
  createdAt: string;
  updatedAt: string;
  name: string;
  currency: string;
  basePrice: number;
  billingCycle: number;
}

export interface Plan {
  id: string;
  createdAt: string;
  updatedAt: string;
  name: string;
  description: string;
  planPrices: PlanPrice[];
}

export interface TaxRate {
  rate: number;
  description?: string;
}

export interface User {
  id: string;
  createdAt: string;
  updatedAt: string;
  email: string;
  name: string;
  email_verified_at: string;
  vip_ended_at: string;
  phone: string | null;
  address: string | null;
  avatar: string | null;
  ward_id: number;
  district_id: number;
  province_id: number;
  date_of_birth: string;
  short_introduce: string | null;
  introduce: string | null;
  job: string | null;
  deleted_at: string | null;
  package_id: string | null;
  leaner_onboarded_at: string | null;
  creator_onboarded_at: string | null;
  total_courses: number;
  total_media_size_uploaded: number;
  last_view_course_id: string | null;
  total_course_basic_learned: number;
  total_course_pro_learned: number;
  total_course_cert_learned: number;
  all_total_learner: number;
  role_id: number;
  vip_upgraded_at: string | null;
  accessToken: string | null;
  refreshToken: string | null;
  total_followers: number;
  is_vip: boolean;
  is_trial: boolean;
  user_favorite_count: number;
  has_completed_creator_tutorial: boolean;
}

export interface PaymentToken {
  id: string;
  gateway: string;
  tokenExpDate: string;
  cardNumber: string;
  cardType: string;
  bankCode: string;
  createdAt: string;
  updatedAt: string;
  createdBy: User;
}

export interface UserSubscription {
  id: string;
  status: 'ACTIVE' | 'INACTIVE' | 'CANCELLED' | 'PENDING';
  planId: string;
  planPriceId: string;
  planPrice: PlanPrice;
  startDate: string;
  endDate: string | null;
  nextBillingDate: string;
  netAmount: number;
  taxAmount: number;
  createdAt: string;
  updatedAt: string;
  canceledAt: string | null;
  plan?: Plan;
  paymentToken?: PaymentToken;
  createdBy?: User;
  updatedBy?: User | null;
}

export interface InitSubscriptionRequest {
  request_key: string;
  payment_gateway: string;
  plan_price_id: string;
  curr_code: string;
  locale: string;
  add_data?: string;
}

export interface InitSubscriptionResponse {
  payment_url?: string;
  transaction_id?: string;
  request_key?: string;
  // VNPay specific fields for POST form redirect
  ispTxnId?: string;
  tmnCode?: string;
  dataKey?: string;
}

export interface Transaction {
  id: string;
  type: 'INITIAL' | 'RENEWAL' | 'REFUND';
  status: 'SUCCESS' | 'FAIL';
  paidAt: string;
  netAmount: number;
  taxAmount: number;
  amountTotal: number;
  gateway: string;
  createdAt: string;
  subscription: {
    id: string;
    planId: string;
    planPriceId: string;
  };
}

export interface Receipt {
  id: string;
  serialNumber: number;
  displayName: string;
  transactionId: string;
  netAmount: number;
  taxAmount: number;
  gateway: string;
  data: {
    startDate: string;
    endDate: string;
    metadata: {
      plan: string;
      discount: string;
    };
  };
  createdAt: string;
  subscriptionId: string;
}

export interface ChangePaymentMethodRequest {
  request_key: string;
  subscription_id: string;
  locale: string;
  add_data?: string;
}

export const getPaymentPlans = async (): Promise<Plan[]> => {
  const response = await fetcher<Plan[]>(API_ENDPOINTS.PAYMENTS.GET.PLANS, {
    method: 'GET',
  });

  if (!response.success) {
    throw new Error(response.message);
  }

  return response.data as Plan[];
};

export const getTaxRate = async (): Promise<TaxRate> => {
  const response = await fetcher<TaxRate>(API_ENDPOINTS.PAYMENTS.GET.TAX_RATE, {
    method: 'GET',
  });

  if (!response.success) {
    throw new Error(response.message);
  }

  return response.data as TaxRate;
};

export const getUserSubscriptions = async (
  status?: 'ACTIVE' | 'INACTIVE' | 'CANCELLED' | 'PENDING',
): Promise<UserSubscription[]> => {
  const url = status
    ? `${API_ENDPOINTS.PAYMENTS.GET.USER_SUBSCRIPTIONS}?status=${status}`
    : API_ENDPOINTS.PAYMENTS.GET.USER_SUBSCRIPTIONS;

  const response = await fetcher<UserSubscription[]>(url, {
    method: 'GET',
  });

  if (!response.success) {
    throw new Error(response.message);
  }

  return response.data as UserSubscription[];
};

export const getSubscriptionById = async (subscriptionId: string): Promise<UserSubscription> => {
  const response = await fetcher<UserSubscription>(`${API_ENDPOINTS.PAYMENTS.GET.SUBSCRIPTION_BY_ID}/${subscriptionId}`, {
    method: 'GET',
  });

  if (!response.success) {
    throw new Error(response.message);
  }

  return response.data as UserSubscription;
};

export const initSubscription = async (data: InitSubscriptionRequest): Promise<InitSubscriptionResponse> => {
  const response = await fetcher<InitSubscriptionResponse>(API_ENDPOINTS.PAYMENTS.POST.INIT_SUBSCRIPTION, {
    method: 'POST',
    body: JSON.stringify(data),
  });

  if (!response.success) {
    throw new Error(response.message);
  }

  return response.data as InitSubscriptionResponse;
};

export const getTransactions = async (): Promise<Transaction[]> => {
  const response = await fetcher<Transaction[]>(API_ENDPOINTS.PAYMENTS.GET.TRANSACTIONS, {
    method: 'GET',
  });

  if (!response.success) {
    throw new Error(response.message);
  }

  return response.data as Transaction[];
};

export const getTransactionReceipt = async (subscriptionId: string, transactionId: string): Promise<Receipt> => {
  const response = await fetcher<Receipt>(
    `${API_ENDPOINTS.PAYMENTS.GET.TRANSACTION_RECEIPT}/${subscriptionId}/transactions/${transactionId}/receipt`,
    {
      method: 'GET',
    },
  );

  if (!response.success) {
    throw new Error(response.message);
  }

  return response.data as Receipt;
};

export const cancelSubscription = async (subscriptionId: string): Promise<void> => {
  const response = await fetcher<void>(API_ENDPOINTS.PAYMENTS.POST.CANCEL_SUBSCRIPTION, {
    method: 'POST',
    body: JSON.stringify({
      subscription_id: subscriptionId,
    }),
  });

  if (!response.success) {
    throw new Error(response.message);
  }
};

export const changePaymentMethod = async (data: ChangePaymentMethodRequest): Promise<InitSubscriptionResponse> => {
  const response = await fetcher<InitSubscriptionResponse>(API_ENDPOINTS.PAYMENTS.POST.CHANGE_PAYMENT_METHOD, {
    method: 'POST',
    body: JSON.stringify(data),
  });

  if (!response.success) {
    throw new Error(response.message);
  }

  return response.data as InitSubscriptionResponse;
};
