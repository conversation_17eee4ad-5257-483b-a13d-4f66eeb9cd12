import { API_ENDPOINTS } from '@/constants/api';
import { fetcher } from '@/lib/fetcher';

export interface RequestOtpRequest {
  email: string;
}

export interface RequestOtpResponse {
  id: string;
}

export interface VerifyOtpRequest {
  email: string;
  otp: string;
  type: string;
}

export interface VerifyOtpResponse {
  success: boolean;
}

export const requestSubscriptionOtp = async (data: RequestOtpRequest): Promise<RequestOtpResponse> => {
  const response = await fetcher<RequestOtpResponse>(API_ENDPOINTS.USERS.POST.REQUEST_SUBSCRIPTION_OTP, {
    method: 'POST',
    body: JSON.stringify(data),
  });

  if (!response.success) {
    throw new Error(response.message);
  }

  return response.data as RequestOtpResponse;
};

export const verifySubscriptionOtp = async (data: VerifyOtpRequest): Promise<VerifyOtpResponse> => {
  const response = await fetcher<VerifyOtpResponse>(API_ENDPOINTS.USERS.POST.VERIFY_OTP, {
    method: 'POST',
    body: JSON.stringify(data),
  });

  if (!response.success) {
    throw new Error(response.message);
  }

  return response.data as VerifyOtpResponse;
};