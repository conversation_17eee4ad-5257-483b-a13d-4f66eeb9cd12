'use client';

import { Modal, Typography } from '@/components/ui';
import { HttpStatusCode } from '@/constants/api';
import React from 'react';
import { ErrorKeys } from '../constants/errors.constants';

type EmailActivationDetectorProps = {
  error?: { statusCode?: number; errorKey?: string };
  open?: boolean;
};

const EmailActivationDetector = (props: EmailActivationDetectorProps) => {
  const { error } = props;
  const [open, setOpen] = React.useState(false);

  React.useEffect(() => {
    const hasError =
      error?.statusCode === HttpStatusCode.FORBIDDEN && error?.errorKey === ErrorKeys.USER_EMAIL_UNVERIFIED;
    const isOpen = props.open;
    if (hasError || isOpen) {
      setOpen(true);
    }
  }, [error, props.open]);

  if (open) {
    return (
      <Modal open={open} onClose={() => setOpen(false)} title="Kích hoạt email" footer={null}>
        <div className="flex items-center justify-center px-4 pb-10 pt-4">
          <Typography variant="bodyLg">Vui lòng kiểm tra email để kích hoạt tài khoản.</Typography>
        </div>
      </Modal>
    );
  }

  return null;
};

export default EmailActivationDetector;
