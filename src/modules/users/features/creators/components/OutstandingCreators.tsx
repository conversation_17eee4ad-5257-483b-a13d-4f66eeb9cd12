'use client';

import { Button, Typography } from '@/components/ui';
import { useFavoriteCreator } from '@/modules/courses/hooks/useFavoriteCreator';
import { useCreatorsQuery } from '@/modules/users/features/creators/hooks/useCreatorsQuery';
import { getOutstandingCreators } from '@/modules/users/services/creator.service';
import { UserInfo } from '@/type';
import { ChevronLeftIcon, ChevronRightIcon } from '@heroicons/react/24/outline';
import 'swiper/css';
import 'swiper/css/navigation';
import { Navigation } from 'swiper/modules';
import { Swiper, SwiperSlide } from 'swiper/react';
import CreatorItem from './CreatorItem';

const OutstandingCreators = ({ creators }: { creators: UserInfo[] }) => {
  const { creators: creatorsData } = useCreatorsQuery({
    initialData: creators,
    serviceFn: async () => {
      const res = await getOutstandingCreators();
      return res.data;
    },
  });

  const { onFavorite, onUpdateQuery: onUpdateCreatorQuery } = useFavoriteCreator();

  const handleUpdateCreatorData = (authorInfo: UserInfo) => {
    onFavorite({
      authorId: authorInfo.id,
      isFavorite: !authorInfo.isFollowing,
      onSuccess: () => {
        onUpdateCreatorQuery((oldData) => {
          const newCreators = oldData?.map((creator) => {
            if (creator.id === authorInfo.id) {
              return { ...creator, isFollowing: !authorInfo.isFollowing };
            }
            return creator;
          });

          return newCreators || [];
        });
      },
    });
  };

  return (
    <div className="flex flex-col gap-4">
      <Typography variant="headlineSm">Edu-Creator nổi bật</Typography>

      <div className="relative">
        <Swiper
          className="swiper-custom-navigation"
          spaceBetween={16}
          slidesPerView="auto"
          navigation={{
            nextEl: '.custom-next',
            prevEl: '.custom-prev',
          }}
          modules={[Navigation]}
        >
          {creatorsData?.length === 0 ? (
            <div className="flex h-60 w-full items-center justify-center">
              <Typography variant="labelMd">Không có Edu-creator nổi bật</Typography>
            </div>
          ) : (
            creatorsData?.map((authorInfo) => (
              <SwiperSlide key={authorInfo.id} className="!w-auto">
                <CreatorItem authorInfo={authorInfo} onFavorite={() => handleUpdateCreatorData(authorInfo)} />
              </SwiperSlide>
            ))
          )}

          {/*eslint-disable-next-line tailwindcss/no-custom-classname */}
          <div className="custom-prev absolute left-4 top-1/2 z-10 -translate-y-5 cursor-pointer">
            <Button variant="tertiary">
              <ChevronLeftIcon className="size-6 text-primary_text" />
            </Button>
          </div>
          {/*eslint-disable-next-line tailwindcss/no-custom-classname */}
          <div className="custom-next absolute right-4 top-1/2 z-10 -translate-y-5 cursor-pointer">
            <Button variant="tertiary">
              <ChevronRightIcon className="size-6 text-primary_text" />
            </Button>
          </div>
        </Swiper>
      </div>
    </div>
  );
};

export default OutstandingCreators;
