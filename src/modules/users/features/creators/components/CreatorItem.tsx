'use client';

import { Icon } from '@/components/client';

import { Button, Typography } from '@/components/ui';
import { routePaths } from '@/config';
import DotIcon from '@/icons/DotIcon';
import { cn } from '@/lib/utils';
import { UserInfo } from '@/type';
import { avatarImgUrl } from '@/utils';
import { StarIcon } from '@heroicons/react/20/solid';
import { StarIcon as StarIconOutline } from '@heroicons/react/24/outline';
import { useRouter } from 'next/navigation';

const CreatorInfo = ({ authorInfo }: { authorInfo: UserInfo }) => {
  return (
    <div className="flex flex-col gap-1">
      <Typography variant="labelLg" className="text-white">
        {authorInfo.name}
      </Typography>

      <Typography variant="labelSm" className="text-secondary_text_reversed">
        Giảng viên
      </Typography>
      <div className="flex items-center gap-2">
        <Typography variant="labelSm" className="text-secondary_text_reversed">
          {authorInfo?.all_total_learner || 0} Ng<PERSON><PERSON><PERSON> học
        </Typography>

        <Icon icon={<DotIcon />} className="flex size-2 items-center" />

        <Typography variant="labelSm" className="text-secondary_text_reversed">
          {authorInfo?.total_courses || 0} khóa học
        </Typography>
      </div>
    </div>
  );
};

type CreatorItemProps = {
  authorInfo: UserInfo;
  isUpdatingFavorite?: boolean;
  onFavorite?: () => void;
};

const CreatorItem = (props: CreatorItemProps) => {
  const { authorInfo, isUpdatingFavorite = false, onFavorite = () => {} } = props;
  const router = useRouter();

  const handleGotoCreator = () => {
    router.push(`${routePaths.profile.children.creator.path}/${authorInfo.id}`);
  };

  return (
    <div
      className={cn('relative size-[240px] cursor-pointer rounded-xl border border-neutral-200')}
      onClick={handleGotoCreator}
    >
      <div
        className={'absolute z-10 size-full rounded-xl'}
        style={{
          backgroundImage: `url(${authorInfo.avatar || avatarImgUrl})`,
          backgroundSize: 'cover',
          backgroundPosition: 'center',
        }}
      >
        <div
          className={cn(
            'flex flex-col justify-end gap-3',
            'h-full rounded-xl p-4',
            'bg-gradient-to-t from-[#080321] to-transparent',
            'hover:shadow-xl',
          )}
        >
          <CreatorInfo authorInfo={authorInfo} />

          <div>
            <Button
              className="w-full"
              variant="secondary-reversed"
              size="medium"
              endIcon={authorInfo.isFollowing ? <StarIcon className="text-yellow-500" /> : <StarIconOutline />}
              loading={isUpdatingFavorite}
              onClick={(e) => {
                e.stopPropagation();
                onFavorite();
              }}
            >
              <Typography className="text-white" variant="labelMd">
                {authorInfo?.isFollowing ? 'Đang theo dõi' : 'Theo dõi'}
              </Typography>
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CreatorItem;
