'use client';

import { Button, Typography } from '@/components/ui';
import { usePreventHydration } from '@/hooks';
import { useFavoriteCreator } from '@/modules/courses/hooks/useFavoriteCreator';
import { useCreatorsQuery } from '@/modules/users/features/creators/hooks/useCreatorsQuery';
import { getFavoriteCreators } from '@/modules/users/services/creator.service';
import { UserInfo } from '@/type';
import { ChevronLeftIcon, ChevronRightIcon } from '@heroicons/react/24/outline';
import 'swiper/css';
import 'swiper/css/navigation';
import { Navigation } from 'swiper/modules';
import { Swiper, SwiperSlide } from 'swiper/react';
import CreatorItem from './CreatorItem';

const FollowedCreators = ({ followedCreators }: { followedCreators: UserInfo[] }) => {
  usePreventHydration();

  const { creators: followedCreatorsData } = useCreatorsQuery({
    initialData: followedCreators,
    serviceFn: async () => {
      const res = await getFavoriteCreators();
      return res.data;
    },
  });

  const { onFavorite, onUpdateQuery: onUpdateCreatorQuery } = useFavoriteCreator();

  const handleUpdateCreatorData = (authorInfo: UserInfo) => {
    onFavorite({
      authorId: authorInfo.id,
      isFavorite: !authorInfo.isFollowing,
      onSuccess: () => {
        onUpdateCreatorQuery((oldData) => {
          const newCreators = oldData?.filter((creator) => creator.id !== authorInfo.id);
          return newCreators || [];
        });
      },
    });
  };

  return (
    <div className="flex flex-col gap-4">
      <Typography variant="headlineSm">Edu-Creator đang theo dõi</Typography>

      <div className="relative">
        <Swiper
          className="swiper-custom-navigation"
          spaceBetween={16}
          slidesPerView="auto"
          navigation={{
            nextEl: '.custom-next',
            prevEl: '.custom-prev',
          }}
          modules={[Navigation]}
        >
          {followedCreatorsData?.length === 0 ? (
            <div className="flex h-60 w-full items-center justify-center">
              <Typography>Bạn chưa thêm Edu-Creator nào vào danh sách theo dõi</Typography>
            </div>
          ) : (
            followedCreatorsData?.map((authorInfo) => (
              <SwiperSlide key={authorInfo.id} className="!w-auto">
                <CreatorItem authorInfo={authorInfo} onFavorite={() => handleUpdateCreatorData(authorInfo)} />
              </SwiperSlide>
            ))
          )}

          {/*eslint-disable-next-line tailwindcss/no-custom-classname */}
          <div className="custom-prev absolute left-4 top-1/2 z-10 -translate-y-5 cursor-pointer">
            <Button variant="tertiary">
              <ChevronLeftIcon className="size-6 text-primary_text" />
            </Button>
          </div>
          {/*eslint-disable-next-line tailwindcss/no-custom-classname */}
          <div className="custom-next absolute right-4 top-1/2 z-10 -translate-y-5 cursor-pointer">
            <Button variant="tertiary">
              <ChevronRightIcon className="size-6 text-primary_text" />
            </Button>
          </div>
        </Swiper>
      </div>
    </div>
  );
};

export default FollowedCreators;
