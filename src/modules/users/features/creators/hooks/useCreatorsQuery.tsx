import { QUERY_KEYS } from '@/constants/query-keys';
import { UserInfo } from '@/type';
import { useQuery } from 'react-query';

export const useCreatorsQuery = ({
  initialData,
  serviceFn,
}: {
  initialData: UserInfo[];
  serviceFn: () => Promise<UserInfo[]>;
}) => {
  const { data: outstandingCreatorsData } = useQuery({
    queryKey: [QUERY_KEYS.FAVORITE_CREATORS],
    initialData,
    queryFn: serviceFn,
  });

  return { creators: outstandingCreatorsData };
};
