import { API_ENDPOINTS } from '@/constants/api';
import { fetcher } from '@/lib/fetcher';
import { CourseListBase } from '@/modules/courses/services/course.service';
import { PaginatedList, PaginatedQueryBase, UserInfo } from '@/type';
import queryString from 'query-string';

const transformPaginatedListResponse = <T>(res: CourseListBase<T> | null) => {
  if (!res) {
    return { count: 0, data: [], limit: 0, page: 0 };
  }

  const { count, data, limit, page } = res;
  const dataResolved = { count, data, limit, page };
  return dataResolved;
};

export const getFavoriteCreators = async (payload?: Partial<PaginatedQueryBase>) => {
  const { page = 0, limit = 10 } = payload ?? {};

  const query = queryString.stringifyUrl({ url: API_ENDPOINTS.COURSES.GET.FAVORITE_CREATORS, query: { page, limit } });

  const { data } = await fetcher<PaginatedList<UserInfo>>(query);

  const dataTransformed = transformPaginatedListResponse(data);
  return dataTransformed;
};

export const getOutstandingCreators = async (payload?: Partial<PaginatedQueryBase>) => {
  const query = queryString.stringifyUrl({
    url: API_ENDPOINTS.USERS.GET.OUTSTANDING_CREATORS,
    query: { page: 0, limit: 10, ...payload },
  });

  const { data } = await fetcher<CourseListBase<UserInfo>>(query);
  const dataTransformed = transformPaginatedListResponse(data);
  return dataTransformed;
};
