import { Icon, SmartLink } from '@/components/client';
import { Typography } from '@/components/ui';
import { routePaths } from '@/config';
import { Lecture, Section } from '@/modules/courses/types/course.type';
import { formatApiUrl } from '@/utils/url.util';
import { PlayCircleIcon } from '@heroicons/react/20/solid';
import { useParams } from 'next/navigation';

const FreeLectureLink = ({ lecture, section }: { lecture: Lecture; section: Section }) => {
  const params = useParams<{ id: string }>();

  const lectureUrl = formatApiUrl(routePaths.learner.children.lecture.path, {
    courseId: params?.id,
    sectionId: section?.id || '',
    lectureId: lecture?.id || '',
  });

  return (
    <SmartLink href={lectureUrl}>
      <div className="flex items-center gap-0.5">
        <Icon icon={<PlayCircleIcon className="text-primary-500" />} />
        <Typography variant="bodyMd" className="text-primary-500">
          <PERSON>em thử miễn phí
        </Typography>
      </div>
    </SmartLink>
  );
};

export default FreeLectureLink;
