'use client';
import { Icon, SmartLink } from '@/components/client';
import { Typography } from '@/components/ui';
import { cn } from '@/lib/utils';
import { StarIcon as StarIconOutline } from '@heroicons/react/24/outline';
import { PlayIcon, StarIcon } from '@heroicons/react/24/solid';

import Image from 'next/image';
import React from 'react';

export type CourseItemProps = {
  courseData: { courseName: string; courseId: string; courseThumbnail: string; courseUrl: string };

  canFavorite?: boolean;

  isFavorite?: boolean;
  creatorName: string;
  topicName?: string;
  footer?: React.ReactNode;

  onFavorite?: (isFavorite: boolean) => void;
};

function CourseItem(props: Readonly<CourseItemProps>) {
  const { courseData, isFavorite, creatorName, topicName, footer, onFavorite } = props;

  const [isHoverImage, setIsHoverImage] = React.useState(false);

  return (
    <div
      className={cn(
        'flex h-full cursor-pointer flex-col gap-3 p-3',
        'rounded-xl border border-neutral-100',
        'hover:border-neutral-100 hover:shadow-xl',
      )}
    >
      <div className="relative" onMouseEnter={() => setIsHoverImage(true)} onMouseLeave={() => setIsHoverImage(false)}>
        <SmartLink className="block" href={courseData.courseUrl} scroll={true}>
          <Image
            src={courseData.courseThumbnail}
            alt="image_course"
            height={200}
            width={238}
            className={cn('h-[180px] w-full rounded-lg object-cover')}
          />
          <div
            className={cn(
              'absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2',
              'rounded-full bg-base-white-50 p-2 transition-all duration-300',
              !isHoverImage && 'hidden',
            )}
          >
            <Icon icon={<PlayIcon className="text-white" />} />
          </div>
        </SmartLink>

        {onFavorite && (
          <div
            className={cn(
              'absolute right-0 top-0 z-10 -translate-x-[8px] translate-y-[8px]',
              'rounded-full bg-base-black-50 p-2 transition-all duration-300',
              'hover:bg-base-black-40 cursor-pointer',
            )}
            onClick={() => onFavorite(!isFavorite)}
            role="button"
            aria-label={isFavorite ? 'Remove from favorites' : 'Add to favorites'}
          >
            <Icon
              icon={
                isFavorite ? (
                  <StarIcon className="text-yellow-500" />
                ) : (
                  <StarIconOutline className="text-base-white-50" />
                )
              }
            />
          </div>
        )}
      </div>

      <SmartLink className="block" href={courseData.courseUrl} scroll={true}>
        <div className="flex size-full flex-col justify-between gap-3">
          <div className="flex flex-col gap-1">
            {topicName && (
              <Typography variant="labelSm" title={topicName} className="uppercase text-primary-500">
                {topicName}
              </Typography>
            )}
            <Typography variant="labelLg">{courseData.courseName}</Typography>
            <Typography variant="labelMd" className="text-secondary_text">
              {creatorName}
            </Typography>
          </div>

          {footer}
        </div>
      </SmartLink>
    </div>
  );
}

export default CourseItem;
