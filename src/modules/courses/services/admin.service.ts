import { API_ENDPOINTS } from '@/constants/api';
import { fetcher } from '@/lib/fetcher';
import { CourseInfo, Lecture } from '@/modules/courses/types/course.type';
import { Question, Test } from '@/modules/courses/types/test.type';
import { formatApiUrl } from '@/utils/url.util';

export type GetCoursePayload = {
  courseId: string;
};

export type GetLecturePayload = {
  courseId: string;
  sectionId: string;
  lectureId: string;
};

export type GetTestPayload = {
  courseId: string;
  sectionId: string;
  testId: string;
};

interface FileInfoResponse {
  id: string;
  fileName: string;
  fileUrl: string;
  fileSize: number;
}

interface QuestionOptionResponse {
  option_index: number;
  option_name: string;
  option_thumbnail_image_file: FileInfoResponse;
}

interface QuestionResponse {
  id: string;
  question_type_id: number;
  question_name: string;
  question_image_file: FileInfoResponse;
  video_file: FileInfoResponse;
  question_options: QuestionOptionResponse[];
  question_answers: string[];
  correct_answer: number[];
  question_required: boolean;
  question_duration: string;
  reply_right_answer: string;
  reply_wrong_answer: string;
  background_color: string;
  sort_index: number;
}

interface TestResponse {
  id: string;
  test_name: string;
  created_at: string;
  updated_at: string;
  min_correct_answer: number;
  has_limit_time: number;
  limit_time: number;
  questions: QuestionResponse[];
}

const mapQuestionOption = (option: QuestionOptionResponse) => {
  return {
    optionIndex: option.option_index,
    optionName: option.option_name,
    optionThumbnailImageFile: option.option_thumbnail_image_file,
  };
};

const mapQuestion = (question: QuestionResponse): Question => {
  return {
    id: question.id,
    questionTypeId: question.question_type_id,
    questionName: question.question_name,
    questionImageFile: question.question_image_file || null,
    videoFile: question.video_file,
    questionOptions: question.question_options.map(mapQuestionOption),
    correctAnswer: question.correct_answer,
    sortIndex: question.sort_index,
    questionImage: question.question_image_file?.fileUrl || '',
  } satisfies Question;
};

const mapTestResponse = (test: TestResponse) => {
  return {
    id: test.id,
    testName: test.test_name,
    createdAt: test.created_at,
    updatedAt: test.updated_at,
    minCorrectAnswer: test.min_correct_answer,
    hasLimitTime: test.has_limit_time,
    limitTime: test.limit_time,
    questions: test.questions.map(mapQuestion),
  } satisfies Test;
};

export class AdminPreviewServices {
  static async getCourse({ courseId }: GetCoursePayload): Promise<CourseInfo | null> {
    const url = formatApiUrl(API_ENDPOINTS.ADMIN_PREVIEW.GET.COURSE, { courseId });
    const response = await fetcher<CourseInfo>(url);
    return response.data;
  }

  static async getLecture(payload: GetLecturePayload) {
    const url = formatApiUrl(API_ENDPOINTS.ADMIN_PREVIEW.GET.LECTURE, {
      courseId: payload.courseId,
      sectionId: payload.sectionId,
      lectureId: payload.lectureId,
    });
    const response = await fetcher<Lecture>(url);
    return response.data;
  }

  static async getTest(payload: GetTestPayload) {
    const url = formatApiUrl(API_ENDPOINTS.ADMIN_PREVIEW.GET.TEST, {
      courseId: payload.courseId,
      sectionId: payload.sectionId,
      testId: payload.testId,
    });

    const response = await fetcher<TestResponse>(url);
    if (!response.data) return null;
    return mapTestResponse(response.data);
  }
}
