import { API_ENDPOINTS, HttpMethod } from '@/constants/api';
import { fetcher } from '@/lib/fetcher';
import { PaginatedQueryBase } from '@/type';
import { formatApiUrl } from '@/utils/url.util';
import queryString from 'query-string';
import {
  LessonCreateRequest,
  LessonDeleteRequest,
  LessonEditRequest,
  PublishCourseRequest,
  ReviewCourseRequest,
  SectionCreateRequest,
  SectionEditRequest,
} from '../types/course-request.type';
import { CourseInfo, CoursesByFilterParams, Lecture, Topic, UserCourse } from '../types/course.type';

export type CourseListBase<T> = PaginatedQueryBase & {
  count: number;
  data: T[];
};

const transformCourseResponse = <T>(res: CourseListBase<T> | null) => {
  if (!res) {
    return { count: 0, data: [], limit: 0, page: 0 };
  }

  const { count, data, limit, page } = res;
  const dataResolved = { count, data, limit, page };
  return dataResolved;
};

export const getCourseByIdService = async (courseId: string): Promise<CourseInfo | null> => {
  const response = await fetcher<CourseInfo>(API_ENDPOINTS.COURSES.GET.COURSE_DETAIL.replace(':courseId', courseId));
  return response.data || null;
};

export const publishCourseService = async (payload: PublishCourseRequest): Promise<void> => {
  const url = API_ENDPOINTS.COURSES.PATCH.PUBLISH_COURSE.replace(':courseId', payload.courseId);
  await fetcher(url, { method: HttpMethod.PATCH });
};

export const createSectionService = async (payload: SectionCreateRequest) => {
  const data = {
    section_name: payload.sectionName,
    sort_index: payload.sortIndex,
    section_type: payload.sectionType,
  };

  const url = API_ENDPOINTS.COURSES.POST.SECTION.replace(':courseId', payload.courseId);
  const response = await fetcher<{ id: string }>(url, {
    method: HttpMethod.POST,
    body: JSON.stringify(data),
  });

  return response.data as { id: string };
};

export const deleteSectionService = async (payload: { courseId: string; sectionId: string }) => {
  const url = API_ENDPOINTS.COURSES.DELETE.SECTION.replace(':courseId', payload.courseId).replace(
    ':sectionId',
    payload.sectionId,
  );

  const res = await fetcher<{ id: string }>(url, {
    method: HttpMethod.DELETE,
  });

  return res.data;
};

export const editSectionService = async (payload: SectionEditRequest) => {
  const data = { section_name: payload.sectionName };

  const url = API_ENDPOINTS.COURSES.PUT.SECTION.replace(':courseId', payload.courseId).replace(
    ':sectionId',
    payload.sectionId,
  );
  const response = await fetcher<{ id: string }>(url, {
    method: HttpMethod.PUT,
    body: JSON.stringify(data),
  });

  return response.data;
};

export const createLessonService = async (payload: LessonCreateRequest) => {
  const url = API_ENDPOINTS.COURSES.POST.LECTURE.replace(':courseId', payload.courseId).replace(
    ':sectionId',
    payload.sectionId,
  );

  const data = {
    lecture_name: payload.lectureName,
    lecture_type: payload.lectureType,
    sort_index: payload.sortIndex,
    thumbnail_file_id: payload?.thumbnailFileId,
  };

  const response = await fetcher<{ id: string }>(url, {
    method: HttpMethod.POST,
    body: JSON.stringify(data),
  });

  return response.data;
};

export const editLessonService = async (payload: LessonEditRequest) => {
  const url = API_ENDPOINTS.COURSES.PUT.LECTURE.replace(':courseId', payload.courseId)
    .replace(':sectionId', payload.sectionId)
    .replace(':lectureId', payload.lectureId);

  const data = {
    lecture_name: payload?.lectureName,
    lecture_thumbnail_image_id: payload?.thumbnailFileId,
  };

  const response = await fetcher<{ id: string }>(url, {
    method: HttpMethod.PUT,
    body: JSON.stringify(data),
  });

  return response.data;
};

export const deleteLessonService = async (payload: LessonDeleteRequest) => {
  const url = API_ENDPOINTS.COURSES.DELETE.LECTURE.replace(':courseId', payload.courseId)
    .replace(':sectionId', payload.sectionId)
    .replace(':lectureId', payload.lectureId);

  const response = await fetcher<{ id: string }>(url, { method: HttpMethod.DELETE });

  return response.data;
};

export const swapSectionService = async (payload: {
  courseId: string;
  sectionId: string;
  sourceIndex: number;
  destinationIndex: number;
}) => {
  const url = API_ENDPOINTS.COURSES.PATCH.SWAP_SECTION.replace(':courseId', payload.courseId).replace(
    ':sectionId',
    payload.sectionId,
  );
  const data = { from: payload.sourceIndex, to: payload.destinationIndex };
  const response = await fetcher<{ id: string }>(url, { method: HttpMethod.PATCH, body: JSON.stringify(data) });
  return response.data;
};

export const swapLessonService = async (payload: {
  courseId: string;
  sectionId: string;
  lectureId: string;
  sourceIndex: number;
  destinationIndex: number;
}) => {
  const url = API_ENDPOINTS.COURSES.PATCH.SWAP_LECTURE.replace(':courseId', payload.courseId)
    .replace(':sectionId', payload.sectionId)
    .replace(':lectureId', payload.lectureId);

  const data = { from: payload.sourceIndex, to: payload.destinationIndex };

  const response = await fetcher<{ id: string }>(url, { method: HttpMethod.PATCH, body: JSON.stringify(data) });
  return response.data;
};

export const getLessonById = async (payload: { courseId: string; sectionId: string; lectureId: string }) => {
  const url = API_ENDPOINTS.COURSES.GET.LECTURE_DETAIL.replace(':courseId', payload.courseId)
    .replace(':sectionId', payload.sectionId)
    .replace(':lectureId', payload.lectureId);

  const response = await fetcher<Lecture>(url, { method: HttpMethod.GET });
  return response.data;
};

export const updateFileLessonService = async (payload: {
  courseId: string;
  sectionId: string;
  lectureId: string;
  payload: { fileId: string | undefined };
}) => {
  const url = API_ENDPOINTS.COURSES.PATCH.LECTURE_VIDEO.replace(':courseId', payload.courseId)
    .replace(':sectionId', payload.sectionId)
    .replace(':lectureId', payload.lectureId);

  const body = JSON.stringify({ file_id: payload.payload.fileId });

  const response = await fetcher<{ id: string }>(url, { method: HttpMethod.PATCH, body });
  return response.data;
};

export const createFaqService = async (payload: {
  courseId: string;
  faqs: {
    question: string;
    answer: string;
    sort_index: number;
  }[];
}) => {
  const url = API_ENDPOINTS.COURSES.POST.CREATE_FAQ.replace(':courseId', payload.courseId);
  const body = JSON.stringify({ faqs: payload.faqs });
  const response = await fetcher<{ id: string }>(url, {
    method: HttpMethod.POST,
    body,
  });
  return response.data;
};

export const editFaqService = async (payload: {
  courseId: string;
  faqId: string;
  question: string;
  answer: string;
}) => {
  const url = API_ENDPOINTS.COURSES.PUT.EDIT_FAQ.replace(':courseId', payload.courseId).replace(
    ':faqId',
    payload.faqId,
  );
  const body = JSON.stringify({ question: payload.question, answer: payload.answer });
  const response = await fetcher<{ id: string }>(url, {
    method: HttpMethod.PUT,
    body,
  });
  return response.data;
};

export const deleteFaqService = async (payload: { courseId: string; faqId: string }) => {
  const url = API_ENDPOINTS.COURSES.DELETE.DELETE_FAQ.replace(':courseId', payload.courseId).replace(
    ':faqId',
    payload.faqId,
  );
  const response = await fetcher<{ id: string }>(url, { method: HttpMethod.DELETE });
  return response.data;
};

export const swapFaqService = async (payload: { courseId: string; from: number; to: number }) => {
  const url = API_ENDPOINTS.COURSES.PATCH.SWAP_FAQ.replace(':courseId', payload.courseId);
  const body = JSON.stringify({ from: payload.from, to: payload.to });
  const response = await fetcher<{ id: string }>(url, { method: HttpMethod.PATCH, body });
  return response.data;
};

export const getFavoriteCourses = async (payload?: Partial<PaginatedQueryBase>) => {
  const { page = 0, limit = 10 } = payload ?? {};

  const query = queryString.stringifyUrl({ url: API_ENDPOINTS.COURSES.GET.FAVORITE_COURSES, query: { page, limit } });

  const { data } = await fetcher<CourseListBase<CourseInfo>>(query);

  const dataTransformed = transformCourseResponse(data);
  return dataTransformed;
};

export const getUserCourses = async (payload: Partial<PaginatedQueryBase> & { userId: string }) => {
  const query = queryString.stringifyUrl({
    url: API_ENDPOINTS.COURSES.GET.USER_COURSES,
    query: { page: 0, limit: 10, ...payload },
  });

  const { data: res } = await fetcher<CourseListBase<UserCourse>>(query);
  const data = transformCourseResponse(res);
  return data;
};

export const updateFavoriteCourseService = async (payload: { courseId: string; isFavorite: boolean }) => {
  const url = formatApiUrl(API_ENDPOINTS.COURSES.POST.FAVORITE_COURSE, { courseId: payload.courseId });

  const res = await fetcher<{ id: string }>(url, {
    method: HttpMethod.POST,
    body: JSON.stringify({ favorite: payload.isFavorite }),
  });

  return res?.data;
};

export const markLectureCompleteService = async (payload: {
  courseId: string;
  sectionId: string;
  lectureId: string;
  isCompleted: boolean;
}) => {
  const url = formatApiUrl(API_ENDPOINTS.COURSES.POST.PROCESS_LEARNER_COURSE, {
    courseId: payload.courseId,
    sectionId: payload.sectionId,
    lectureId: payload.lectureId,
  });

  const body = JSON.stringify({ is_complete: payload.isCompleted });
  const res = await fetcher(url, { method: HttpMethod.POST, body });

  return res?.data;
};

export const reviewCourseService = async (request: { courseId: string; data: ReviewCourseRequest }) => {
  const url = formatApiUrl(API_ENDPOINTS.COURSES.POST.COURSE_REVIEW, { courseId: request.courseId });

  const body = JSON.stringify(request.data);
  const res = await fetcher(url, { body, method: HttpMethod.POST });
  return res?.data;
};

export const getOutstandingCourses = async (payload: Partial<PaginatedQueryBase>) => {
  const query = queryString.stringifyUrl({
    url: API_ENDPOINTS.COURSES.GET.OUTSTANDING_COURSES,
    query: { page: 0, limit: 10, ...payload },
  });

  const { data: res } = await fetcher<CourseListBase<CourseInfo>>(query);
  const data = transformCourseResponse(res);
  return data;
};

export const getPublicCoursesByFilter = async (payload: CoursesByFilterParams) => {
  const { levels, rate, types, topic, limit = 12, page = 0 } = payload;

  const queryParams = { page, limit, types, levels, rate, topic };
  const query = queryString.stringifyUrl({ url: API_ENDPOINTS.COURSES.GET.COURSES_PUBLIC, query: queryParams });

  const { data: res } = await fetcher<CourseListBase<CourseInfo>>(query);
  const data = transformCourseResponse(res);
  return data;
};

export const getPublicTopics = async () => {
  const { data } = await fetcher<CourseListBase<Topic>>(API_ENDPOINTS.COURSES.GET.TOPICS_PUBLIC);
  const topics = { ...data, data: data?.data || [] };
  return topics;
};

export const getPublicOutstandingCourses = async (payload: Partial<PaginatedQueryBase>) => {
  const query = queryString.stringifyUrl({
    url: API_ENDPOINTS.COURSES.GET.OUTSTANDING_COURSES_PUBLIC,
    query: { page: 0, limit: 10, ...payload },
  });

  const { data: res } = await fetcher<CourseListBase<CourseInfo>>(query);
  const data = transformCourseResponse(res);
  return data;
};

export const setFreeContentService = async (payload: {
  courseId: string;
  sectionId: string;
  lectureId: string;
  isFreeContent: boolean;
}) => {
  const url = formatApiUrl(API_ENDPOINTS.COURSES.PATCH.SET_FREE_CONTENT, {
    courseId: payload.courseId,
    sectionId: payload.sectionId,
    lectureId: payload.lectureId,
  });
  const body = JSON.stringify({ is_free_content: payload.isFreeContent });
  const res = await fetcher(url, { body, method: HttpMethod.PATCH });
  return res?.data;
};
