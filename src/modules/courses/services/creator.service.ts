import { API_ENDPOINTS, HttpMethod } from '@/constants/api';
import { fetcher } from '@/lib/fetcher';
import { CourseListBase } from '@/modules/courses/services/course.service';
import { PaginatedQueryBase, UserInfo } from '@/type';
import { formatApiUrl } from '@/utils/url.util';
import queryString from 'query-string';

export const updateFavoriteCreatorService = async (request: { authorId: string; isFavorite: boolean }) => {
  const url = formatApiUrl(API_ENDPOINTS.COURSES.POST.FAVORITE_CREATOR, { authorId: request.authorId });

  const query = queryString.stringifyUrl({ url });

  const favorite = request.isFavorite;

  const { data } = await fetcher<CourseListBase<UserInfo>>(query, {
    body: JSON.stringify({ favorite }),
    method: HttpMethod.POST,
  });
  return data;
};

export const getOutstandingCreatorsService = async (payload?: Partial<PaginatedQueryBase>) => {
  const query = queryString.stringifyUrl({
    url: API_ENDPOINTS.USERS.GET.OUTSTANDING_CREATORS,
    query: { page: 0, limit: 10, ...payload },
  });

  const { data: resData } = await fetcher<CourseListBase<UserInfo>>(query);
  const list = resData?.data || [];
  return { ...resData, data: list };
};
