import { CompletedLecture, Package, RelatedCourse, Tag } from '@/features/courses';
import { Test } from '@/features/courses/types/test.type';
import { PaginatedQueryBase, UserInfo } from '@/type';
import { ChapterType, CourseStatusEnum, LessonType } from '../constants/course.const';
import { LibraryFileType } from '../constants/file.const';
import { LectureInteract } from './interaction.type';

export type UserCourse = {
  id: string;
  user: {
    id: string;
    name: string;
    email: string;
  };
  courseId: string;
  isCompleted: number;
  lastViewLecture?: {
    id: string;
    lectureName: string;
    lectureThumbnailImage: string;

    duration: number;
    lectureType: string;
    sectionId: string;
  };
  totalLectures: number;
  countCompletedLectures: number;
  createdAt: string;
  updatedAt: string;
  course: {
    id: string;
    courseName: string;
    courseDescription: string;
    courseThumbnailImage: string;
    courseLevelId: string;
    courseDuration: string;
    totalSections: string;
    totalLearner: string;
    topic: Topic;
    sections: Section[];
    createdBy: UserInfo;
  };

  totalRating: string | null;
  totalRating5: string | null;
  totalRating4: string | null;
  totalRating3: string | null;
  totalRating2: string | null;
  totalRating1: string | null;
  avgRating: string | null;
};

export type Lecture = {
  id: string;
  lectureType: LessonType;
  testId: string | null;
  thumbnailFileId: string | null;
  lectureThumbnailImage: string | null;
  lectureName: string;
  sortIndex: number;
  publish: number;
  createdBy: UserInfo;
  updatedBy: string | null;
  createdAt: string;
  updatedAt: string;
  section: Section;
  duration: number;
  lectureInteracts: LectureInteract[] | null;
  videoId: Video;
  test: Test | null;

  isFreeContent?: boolean;

  //MOCK type
  // slideId: string | null;
  // lectureInteracts: LectureInteract[] | null;
  // slide: Slide | null;
  // user_answers?: UserAnswer[];
  // sectionId: string;
  // lecture_segments: LectureSegment[];
  // lecture_documents: LectureDocument[];
  // video: VideoId | null;
};

export type Section = {
  id: string;
  createdAt: string | null;
  updatedAt: string | null;
  createdBy: string | null;
  updatedBy: string | null;
  sectionName: string;
  sortIndex: number;
  publish: number;
  sectionType: ChapterType;
  testId: string | null;
  learningGoalId: string | null;
  sectionDuration: number;
  lectures: Lecture[];

  // TODO: need to check with BE
  test: Test | null;

  // MOCK: Mock Type
  // learningGoal: Target;
};

export type CourseInfo = {
  id: string;
  createdAt: string;
  updatedAt: string;
  package: Package;
  courseTypeId: number;
  courseDesignId: string | null;
  courseName: string;
  courseDescription: string;
  courseThumbnailImage: string;
  thumbnailFileId: string | null;
  duration: string | null;
  copyright: string | null;
  topic: Topic;
  courseLevelId: string;
  certificateId: string | null;
  organizationId: string | null;
  isSequential: number;
  sortIndex: string | null;
  publish: number;
  active: number;
  updatedBy: string | null;
  totalRating: string | null;
  totalRating5: string | null;
  totalRating4: string | null;
  totalRating3: string | null;
  totalRating2: string | null;
  totalRating1: string | null;
  avgRating: string | null;
  countFeeling1: string;
  countFeeling2: string;
  countFeeling3: string;
  countFeeling4: string;
  totalLearner: string;
  totalSections: string;
  courseDuration: string;
  createdBy: UserInfo;
  sections: Section[] | null;
  courseRelated: RelatedCourse[];
  courseTag: Tag[];
  isFavorite: boolean;
  // userCompletedLectures?: CompletedLecture[];
  totalLectures?: number;
  countCompletedLectures?: number;
  userCompletedLecture?: CompletedLecture[] | null;
  status: CourseStatusEnum;

  userCourse: UserCourse[];
  courseFaq: FAQ[];
  comments: Comment[];
};

export type Video = {
  id: string;
  countUsed: number;
  fileType: LibraryFileType;
  fileName: string;
  fileUrl: string;
  fileDuration: number;
  fileSize: number;
  createdBy: string | null;
  updatedBy: string | null;
  createdAt: string;
  updatedAt: string;
};

export type UserComment = {
  id: string;
  createdAt: string;
  updatedAt: string;
  email: string;
  name: string;
  emailVerifiedAt: string;
  vipEndedAt: string;
  password: string | null;
  phone: string | null;
  avatar: string | null;
  introduce: string | null;
  wardId: number;
  districtId: number;
  provinceId: number;
  totalCourses: number;
  allTotalLearner: number;
  totalCourseCertLearned: number;
  totalCourseProLearned: number;
  totalCourseBasicLearned: number;
  totalFollowers: number;
  shortIntroduce: string | null;
  job: string | null;
  totalMediaSizeUploaded: number;
  roleId: number;
};

export type Comment = {
  id: string;
  updatedBy: string | null;
  user: UserComment;
  parentId: string | null;
  comment: string | null;
  rating: number | null;
  reactions: null;
  active: number;
};

export type FAQ = {
  answer: string;
  createdAt: string;
  id: string;
  question: string;
  sortIndex: number;
  updatedAt: string;
};

export type CoursesBySearchParams = { search: string } & Partial<PaginatedQueryBase>;

export type CoursesByFilterParams = {
  topic?: string;
  rate?: string;
  levels?: string | string[];
  types?: string | string[];
} & Partial<PaginatedQueryBase>;

export type Topic = {
  id: string;
  topicName: string;
  showOnBoard: number;
  icon: string;
  createdAt: string;
  updatedAt: string;
};
