'use client';

import { Typography } from '@/components/ui';
import { useFullScreen, usePreventHydration } from '@/hooks';
import { useClient } from '@/hooks/useClient';
import InteractionOverlay from '@/modules/courses/components/interaction-overlay/InteractionOverlay';
import useVideoInteractions from '@/modules/courses/features/create-course/design-step/hooks/useVideoInteractions';
import VideoControls from '@/modules/courses/features/learn-course/components/video/VideoControls';
import { CourseInfo, Lecture } from '@/modules/courses/types/course.type';
import { PlayerProgressProps } from '@/type/playerProgressProps';
import { useVideoProgressTracking } from 'hooks/useVideoProgressTracking';
import React, { useRef } from 'react';
import { OnProgressProps } from 'react-player/base';
import ReactPlayer, { ReactPlayerProps } from 'react-player/lazy';

type InteractionVideoProps = {
  reactPlayerProps: ReactPlayerProps;
  selectedLecture: Lecture | undefined;
  courseId?: string;
  sectionId?: string;
  isPreviewMode?: boolean;
  userId?: string;
  courseInfo?: CourseInfo; // Add courseInfo prop for header
  showHeader?: boolean; // Add option to show/hide header
};

function BufferingSpin() {
  return (
    <div className={'absolute top-0 z-10 flex size-full items-center justify-center'}>
      <div className="inline-block size-8 animate-spin rounded-full border-4 border-solid border-r-transparent align-[-0.125em] text-primary-500 motion-reduce:animate-[spin_1.5s_linear_infinite]"></div>
    </div>
  );
}

function InteractionVideo(props: InteractionVideoProps) {
  const { reactPlayerProps, selectedLecture, courseId, sectionId, isPreviewMode = false, userId } = props;

  usePreventHydration();

  const { isFullScreen } = useFullScreen();

  const [playerProgress, setPlayerProgress] = React.useState<PlayerProgressProps>({
    playing: false,
    currentTime: 0,
    duration: 0,
    ready: false,
    volume: 100,
    muted: false,
    secondsLoaded: 0,
    seeking: false,
    buffering: false,
  });

  const playerRef = useRef<ReactPlayer>(null);
  const controlVideoRef = useRef<HTMLDivElement>(null);
  const backgroundControlRef = useRef<HTMLButtonElement>(null);

  const fullScreenRef = useRef(null);

  const onHoverVideo = () => {};
  const onFocusOutVideo = () => {};

  const handleChangeProgress = (progress: OnProgressProps) => {
    setPlayerProgress((prev) => ({
      ...prev,
      currentTime: progress.playedSeconds,
      secondsLoaded: progress.loadedSeconds,
      played: progress.played,
      buffered: progress.loaded,
    }));
  };

  const onBuffer = () => setPlayerProgress((prev) => ({ ...prev, buffering: true }));
  const onBufferEnd = () => setPlayerProgress((prev) => ({ ...prev, buffering: false }));

  const { selectedInteraction, isShowInteraction, onCloseInteraction } = useVideoInteractions({
    interactions: selectedLecture?.lectureInteracts || [],
    currentTime: playerProgress.currentTime,
    onPause: () => {
      setPlayerProgress({
        ...playerProgress,
        playing: false,
      });
    },
  });

  const savedTimeRef = useRef<number>(0);
  // const isSeeked = useRef(false);

  usePreventHydration();

  // Track video progress every 5 seconds and restore saved progress
  useVideoProgressTracking({
    courseId: courseId || '',
    lectureId: selectedLecture?.id?.toString() || '',
    sectionId: sectionId || '',
    currentTime: playerProgress.currentTime,
    isPlaying: playerProgress.playing,
    isPreviewMode,
    userId,
    onProgressLoaded: (savedTime: number) => {
      // Store the saved time in a ref to avoid state updates that could cause loops
      if (savedTime > 0) {
        savedTimeRef.current = savedTime;
      }
    },
  });

  const handleVideoReadyWithProgress = () => {
    setPlayerProgress((prevState) => ({
      ...prevState,
      duration: selectedLecture?.videoId.fileDuration ?? 0,
      ready: true,
      buffering: false,
    }));

    // // Restore saved progress if available and current time is less than saved time
    // if (savedTimeRef.current > 0 && playerProgress.currentTime < savedTimeRef.current && !isSeeked.current) {
    //   // Small delay to ensure video is fully ready
    //   setTimeout(() => {
    //     if (playerRef.current) {
    //       isSeeked.current = true;
    //       playerRef.current.seekTo(savedTimeRef.current);
    //     }
    //   }, 100);
    // }
  };

  const isClient = useClient();
  if (!isClient) return null;

  return (
    <div className="flex h-[calc(100vh-72px)] flex-col bg-black" ref={fullScreenRef}>
      <Typography variant="bodyLg" className="absolute translate-x-6 translate-y-3 text-white">
        {selectedLecture?.lectureName}
      </Typography>

      <div
        className="relative size-full"
        onClick={() => {
          if (isShowInteraction) return;
          setPlayerProgress({ ...playerProgress, playing: !playerProgress.playing });
        }}
      >
        {(!playerProgress.ready || playerProgress.buffering) && <BufferingSpin />}

        <ReactPlayer
          width={'100%'}
          height={'100%'}
          fallback={<BufferingSpin />}
          ref={playerRef}
          muted={playerProgress.muted}
          volume={playerProgress.volume / 100}
          playing={playerProgress.playing}
          config={{ file: { attributes: { crossOrigin: 'anonymous' } } }}
          onBuffer={onBuffer}
          onBufferEnd={onBufferEnd}
          onReady={handleVideoReadyWithProgress}
          onProgress={handleChangeProgress}
          onPlay={() => {
            setPlayerProgress({ ...playerProgress, playing: true });
          }}
          onPause={() => {
            setPlayerProgress({ ...playerProgress, playing: false });
          }}
          {...reactPlayerProps}
        />

        <InteractionOverlay
          isVisible={!!isShowInteraction}
          currentInteraction={selectedInteraction}
          onClose={() => {
            onCloseInteraction();
            setPlayerProgress({ ...playerProgress, playing: true });
          }}
        />

        <VideoControls
          fullScreenRef={fullScreenRef}
          isFullScreen={isFullScreen}
          playerProgress={playerProgress}
          selectedLecture={selectedLecture}
          controlVideoRef={controlVideoRef}
          setPlayerProgress={setPlayerProgress}
          onHoverVideo={onHoverVideo}
          onFocusOutVideo={onFocusOutVideo}
          onProgressChanges={(time) => {
            if (playerRef.current) {
              setPlayerProgress((prev) => ({ ...prev, currentTime: time, playing: false }));
              playerRef.current.seekTo(time);

              setTimeout(() => {
                setPlayerProgress((prev) => ({ ...prev, playing: true }));
              }, 200);
            }
          }}
        />
      </div>
    </div>
  );
}

export default InteractionVideo;
