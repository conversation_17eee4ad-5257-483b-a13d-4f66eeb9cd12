import { Icon, VideoProgressSlider } from '@/components/client';
import { Button } from '@/components/ui';
import { secondToHHMMSS } from '@/lib/dateTime';
import { InteractionMarkers } from '@/modules/courses/components';
import VideoSoundControl from '@/modules/courses/features/learn-course/components/video/VideoSoundControl';
import { Lecture } from '@/modules/courses/types/course.type';
import { PlayerProgressProps } from '@/type/playerProgressProps';
import { ArrowsPointingInIcon, ArrowsPointingOutIcon } from '@heroicons/react/24/outline';
import { PauseIcon, PlayIcon } from '@heroicons/react/24/solid';
import { useChapterDrawer } from 'hooks/useChapterDrawer';
import ChaptersIcon from 'icons/ChaptersIcon';
import { RefObject } from 'react';
import 'react-video-seek-slider/styles.css';
import screenfull from 'screenfull';

type VideoControlsProps = {
  fullScreenRef: React.RefObject<null>;
  isFullScreen: boolean;
  selectedLecture: Lecture | undefined;

  setPlayerProgress: (
    playerProgress: PlayerProgressProps | ((prev: PlayerProgressProps) => PlayerProgressProps),
  ) => void;
  onHoverVideo: () => void;
  onFocusOutVideo: () => void;

  onProgressChanges: (currentTime: number) => void;

  playerProgress: PlayerProgressProps;

  controlVideoRef: RefObject<HTMLDivElement | null>;
};

const VideoControls = (props: VideoControlsProps) => {
  const {
    isFullScreen,
    fullScreenRef,
    playerProgress,
    controlVideoRef,
    selectedLecture,

    setPlayerProgress,
    onProgressChanges,
    onHoverVideo,
    onFocusOutVideo,
  } = props;

  const { currentTime, duration, volume, muted, playing, secondsLoaded } = playerProgress ?? {};
  const { handleOpenChapterDrawer } = useChapterDrawer();

  return (
    <div
      ref={controlVideoRef}
      onMouseLeave={onFocusOutVideo}
      onMouseEnter={onHoverVideo}
      className={'absolute bottom-0 w-full bg-black p-2'}
    >
      <div className={'h-4 w-full'}>
        <VideoProgressSlider
          duration={duration}
          currentTime={currentTime}
          bufferTime={secondsLoaded}
          onChange={(time) => {
            onProgressChanges(time);
          }}
        />
      </div>

      <div className="relative z-10 w-full -translate-y-2">
        <InteractionMarkers duration={duration} interactions={selectedLecture?.lectureInteracts || []} />
      </div>

      <div className={'flex h-full items-center justify-between px-4'} onClick={(e) => e.stopPropagation()}>
        <div className={'flex h-full w-4/5 items-center gap-8'}>
          <div className={'flex'}>
            <button
              onClick={() => {
                setPlayerProgress({ ...playerProgress, playing: !playing });
              }}
            >
              {playing ? (
                <Icon icon={<PauseIcon className="text-white" />} />
              ) : (
                <Icon icon={<PlayIcon className="text-white" />} />
              )}
            </button>
          </div>

          <VideoSoundControl
            volume={volume}
            setVolume={(volumeChange) => {
              setPlayerProgress({ ...playerProgress, volume: volumeChange });
            }}
            muted={muted}
            setMuted={(mutedValue) => {
              setPlayerProgress({ ...playerProgress, muted: mutedValue });
            }}
          />

          <span
            className={'inline-block min-w-[100px] text-white'}
          >{`${secondToHHMMSS(currentTime)} / ${secondToHHMMSS(duration)}`}</span>
        </div>

        <div className={'flex items-center gap-2'}>
          {!isFullScreen && (
            <Button
              variant="ghost-reversed"
              size="small"
              title="Xem danh sách chương"
              icon={<ChaptersIcon />}
              onClick={handleOpenChapterDrawer}
            >
              <ChaptersIcon />
            </Button>
          )}

          <Button
            onClick={() => {
              if (!fullScreenRef.current) return;
              screenfull.toggle(fullScreenRef.current);
            }}
            variant="ghost-reversed"
            size="small"
          >
            <Icon icon={isFullScreen ? <ArrowsPointingInIcon /> : <ArrowsPointingOutIcon />} />
          </Button>
        </div>
      </div>
    </div>
  );
};

export default VideoControls;
