'use client';

import { Typography } from '@/components/ui';
import { routePaths } from '@/config';
import { QUERY_KEYS } from '@/constants/query-keys';
import { useNotification } from '@/hooks';
import { ChapterType } from '@/modules/courses/constants/course.const';
import { FinalTestResult } from '@/modules/courses/features/learn-course/components/test/FinalTestResult';
import { QuizResult } from '@/modules/courses/features/learn-course/components/test/QuizResult';
import TestControls from '@/modules/courses/features/learn-course/components/test/TestControls';
import { TestViewMode } from '@/modules/courses/features/learn-course/components/test/test.type';
import { getUncompletedQuestions } from '@/modules/courses/features/learn-course/components/test/test.util';
import useMarkLectureComplete from '@/modules/courses/features/learn-course/hooks/useMarkLectureComplete';
import useTestActions from '@/modules/courses/features/learn-course/hooks/useTestActions';
import { getNextLecture } from '@/modules/courses/features/learn-course/utils/lecture.util';
import { CourseInfo, Section } from '@/modules/courses/types/course.type';
import { Test, UserTestResult } from '@/modules/courses/types/test.type';
import { formatApiUrl } from '@/utils/url.util';
import { useParams, useRouter } from 'next/navigation';
import React from 'react';
import { useQueryClient } from 'react-query';
import TestQuestionContent from './TestQuestionContent';

type TestContainerProps = {
  testInfo?: Test | null;
  courseInfo: CourseInfo;
  testType?: 'quiz' | 'finalTest';
  isAdminPreview?: boolean;
};

const getLatestSubmitTime = (userAnswers: UserTestResult[] | null) => {
  if (!userAnswers) return 0;
  return Math.max(...userAnswers.map((item) => item.times ?? 0));
};

const filterUserAnswers = (userAnswers: UserTestResult[] | null, latestSubmitTime: number) => {
  if (!userAnswers) return [];
  return userAnswers.filter((item) => item.times === latestSubmitTime);
};

const getPreviousLecture = (sections: Section[]) => {
  const filteredSections = sections.filter((section) => section.sectionType === ChapterType.Default);

  const previousSection = filteredSections[filteredSections.length - 1];

  if (!previousSection) return null;

  const lectures = previousSection?.lectures || [];
  const lastLecture = lectures[lectures.length - 1];

  return {
    section: previousSection,
    lecture: lastLecture,
  };
};

const TestContainer = (props: TestContainerProps) => {
  const { testInfo, courseInfo, testType = 'quiz', isAdminPreview = false } = props;

  const params = useParams<{ courseId: string; sectionId: string; lectureId: string }>();

  const router = useRouter();

  const notification = useNotification();

  const queryClient = useQueryClient();

  const { onMarkCompleteLecture, isLoading: isMarkCompleteLoading } = useMarkLectureComplete();

  const { onSubmitTestQuestion, onGetTestAnswers: onGetTestAnswersMutation } = useTestActions();

  const [isNextLoading, setIsNextLoading] = React.useTransition();

  const [currentQuestionIndex, setCurrentQuestionIndex] = React.useState(0);

  const [openConfirmSubmitModal, setOpenConfirmSubmitModal] = React.useState(false);

  const [selectedAnswers, setSelectedAnswers] = React.useState<{ questionIndex: number; answers: number[] }[]>([]);

  const [userAnswers, setUserAnswers] = React.useState<UserTestResult[] | null>(null);

  const [viewMode, setViewMode] = React.useState<TestViewMode>('default');

  const questions = testInfo?.questions || [];

  const isLastQuestion = currentQuestionIndex === questions.length - 1;

  const handleSelectQuestion = (index: number) => {
    setCurrentQuestionIndex(index);
  };

  const handleConfirmAnswer = () => {
    if (isLastQuestion) {
      setOpenConfirmSubmitModal(true);
      return;
    }

    setCurrentQuestionIndex(currentQuestionIndex + 1);
  };

  const handleSubmitTest = () => {
    if (isAdminPreview) {
      setOpenConfirmSubmitModal(false);
      return;
    }

    const uncompletedQuestions = getUncompletedQuestions(selectedAnswers, questions);

    if (uncompletedQuestions.length > 0) {
      notification.error({
        message: 'Vui lòng hoàn thành tất cả câu hỏi trước khi nộp bài',
      });
      setOpenConfirmSubmitModal(false);
      return;
    }

    const submitTestPayload = {
      courseId: params?.courseId || '',
      sectionId: params?.sectionId || '',
      testId: testInfo?.id || '',
      data: selectedAnswers.map((item) => ({
        question_id: questions[item.questionIndex].id,
        answer: item.answers,
      })),
    };

    onSubmitTestQuestion(submitTestPayload, {
      onSuccess: () => {
        onMarkCompleteLecture({ ...params, isCompleted: true });

        queryClient.invalidateQueries({
          queryKey: [QUERY_KEYS.COURSE_DETAIL, params?.courseId || ''],
        });

        onGetTestAnswersMutation(
          {
            courseId: params?.courseId || '',
            sectionId: params?.sectionId || '',
            testId: testInfo?.id || '',
          },
          {
            onSuccess: (data) => {
              setOpenConfirmSubmitModal(false);
              const latestSubmitTime = getLatestSubmitTime(data);
              const filteredData = filterUserAnswers(data, latestSubmitTime);
              setUserAnswers(filteredData);
              setViewMode('viewResult');
            },
          },
        );
      },
    });
  };

  const resetData = () => {
    setViewMode('default');
    setCurrentQuestionIndex(0);
    setSelectedAnswers([]);
    setUserAnswers(null);
  };

  const handleNextToLecture = () => {
    const nextLectureData = getNextLecture({ sections: courseInfo?.sections || [], params });
    if (!nextLectureData) return;

    const { lecture, section } = nextLectureData;

    if (nextLectureData?.section.sectionType === ChapterType.Test) {
      const nextLectureUrl = formatApiUrl(routePaths.learner.children.finalTest.path, {
        courseId: params?.courseId || '',
        sectionId: section.id,
        testId: section.test?.id || '',
      });
      resetData();
      router.push(nextLectureUrl);
      return;
    }

    const nextLectureUrl = formatApiUrl(routePaths.learner.children.lecture.path, {
      courseId: params?.courseId || '',
      sectionId: section.id,
      lectureId: lecture?.id || '',
    });

    resetData();
    router.push(nextLectureUrl);
  };

  const handleGoToReviewCourse = () => {
    const { courseId = '', sectionId = '' } = params;
    const reviewUrl = formatApiUrl(routePaths.learner.children.finalTest.children.review.path, {
      courseId,
      sectionId,
      testId: testInfo?.id || '',
    });

    setIsNextLoading(() => {
      router.push(reviewUrl);
    });
  };

  const handleNextToTest = () => {
    const isFinalTestLastQuestion = testType === 'finalTest' && isLastQuestion;
    if (isFinalTestLastQuestion) {
      handleGoToReviewCourse();
      return;
    }

    if (isLastQuestion) {
      handleNextToLecture();
      return;
    }

    setCurrentQuestionIndex(currentQuestionIndex + 1);
  };

  const renderContent = () => {
    if (viewMode === 'viewResult' && testType === 'finalTest') {
      return (
        <FinalTestResult
          test={testInfo!}
          testResults={userAnswers || []}
          onRetry={() => resetData()}
          onViewAnswer={() => {
            setViewMode('viewAnswer');
            setCurrentQuestionIndex(0);
          }}
          onViewPreviousLecture={() => {
            const previousLectureData = getPreviousLecture(courseInfo?.sections || []);
            if (!previousLectureData) return;

            const { lecture, section } = previousLectureData;

            const previousLectureUrl = formatApiUrl(routePaths.learner.children.lecture.path, {
              courseId: params?.courseId,
              sectionId: section.id,
              lectureId: lecture?.id,
            });
            resetData();
            router.push(previousLectureUrl);
          }}
        />
      );
    }

    if (viewMode === 'viewResult' && testType === 'quiz') {
      return (
        <QuizResult
          test={testInfo!}
          testResults={userAnswers || []}
          onViewAnswer={() => {
            setViewMode('viewAnswer');
            setCurrentQuestionIndex(0);
          }}
          onNext={handleNextToLecture}
        />
      );
    }

    return (
      <TestQuestionContent
        key={currentQuestionIndex}
        isViewAnswer={viewMode === 'viewAnswer'}
        testInfo={testInfo || null}
        selectedAnswers={selectedAnswers}
        currentQuestionIndex={currentQuestionIndex}
        isNextLoading={isNextLoading}
        userAnswers={userAnswers}
        setSelectedAnswers={setSelectedAnswers}
        onNext={handleNextToTest}
        onConfirmAnswer={handleConfirmAnswer}
      />
    );
  };

  return (
    <React.Fragment>
      <div className="flex h-screen flex-col bg-white">
        <div className="shrink-0 bg-black p-4">
          <Typography className="text-white">{testInfo?.testName}</Typography>
        </div>

        <div className="flex flex-1 items-center justify-center py-10">{renderContent()}</div>

        {viewMode === 'viewResult' ? null : (
          <div className="shrink-0">
            <TestControls
              isAdminPreview={isAdminPreview}
              currentQuestionIndex={currentQuestionIndex}
              questions={questions}
              isMarkCompleteLoading={isMarkCompleteLoading}
              selectedAnswers={selectedAnswers}
              userAnswers={userAnswers}
              viewMode={viewMode}
              openConfirmSubmitModal={openConfirmSubmitModal}
              onSelectQuestion={handleSelectQuestion}
              setCurrentQuestionIndex={setCurrentQuestionIndex}
              onSubmitTest={handleSubmitTest}
              setOpenConfirmSubmitModal={setOpenConfirmSubmitModal}
            />
          </div>
        )}
      </div>
    </React.Fragment>
  );
};

export default TestContainer;
