import { Question } from '@/modules/courses/types/test.type';

export const getUncompletedQuestions = (
  selectedAnswers: { questionIndex: number; answers: number[] }[],
  questions: Question[],
) => {
  const uncompletedQuestions = questions.filter((_, index) => {
    return !selectedAnswers.some((answer) => answer.questionIndex === index && answer.answers.length > 0);
  });

  return uncompletedQuestions;
};
