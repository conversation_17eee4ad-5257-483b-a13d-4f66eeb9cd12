import { Drawer, Typography } from '@/components/ui';

import { Icon } from '@/components/client';
import { palette } from '@/config/theme';
import IconCollapsePopup from '@/icons/IconCollapsePopup';
import IconQuestionNotDone from '@/icons/IconQuestionNotDone';
import { cn } from '@/lib/utils';
import { Question, UserTestResult } from '@/modules/courses/types/test.type';
import { CheckCircleIcon, XCircleIcon } from '@heroicons/react/24/solid';
import { TestViewMode } from './test.type';

type QuestionStatusProps = {
  currentQuestionIndex: number;
  questionIndex: number;
  selectedAnswers: { questionIndex: number; answers: number[] }[];
  userAnswers: UserTestResult[] | null;
  viewMode: TestViewMode;
};

const getAnswerStatusIcon = (point: number) => {
  if (point > 0) {
    return <CheckCircleIcon className="size-6 text-green-600" />;
  }
  return <XCircleIcon className="size-6 text-red-500" />;
};

const renderStatusQuestion = ({
  currentQuestionIndex,
  questionIndex,
  selectedAnswers,
  userAnswers,
  viewMode,
}: QuestionStatusProps) => {
  if (currentQuestionIndex === questionIndex) {
    return <IconQuestionNotDone stroke={palette.primary[500]} />;
  }

  if (viewMode === 'viewAnswer' && userAnswers) {
    const questionPoint = userAnswers[questionIndex]?.point ?? 0;
    return getAnswerStatusIcon(questionPoint);
  }

  const hasSelectedAnswer = selectedAnswers.some(
    (item) => item.questionIndex === questionIndex && item.answers.length > 0,
  );

  return hasSelectedAnswer ? <CheckCircleIcon className="text-primary-500" /> : <IconQuestionNotDone />;
};

type TestQuestionListProps = {
  isOpen: boolean;
  questions: Question[];
  currentQuestionIndex: number;
  selectedAnswers: { questionIndex: number; answers: number[] }[];
  userAnswers: UserTestResult[] | null;
  viewMode: TestViewMode;

  onSelectQuestion: (index: number) => void;
  onOpen: (isOpen: boolean) => void;
};

function TestQuestionListDrawer(props: TestQuestionListProps) {
  const {
    isOpen,
    questions,
    currentQuestionIndex,
    selectedAnswers,
    userAnswers,
    viewMode,

    onSelectQuestion,
    onOpen,
  } = props;

  if (!isOpen) return;

  return (
    <Drawer
      open={isOpen}
      onClose={() => onOpen(false)}
      closable
      closeIcon={<IconCollapsePopup />}
      title={'Danh sách câu hỏi'}
      placement="right"
      width={400}
    >
      <div className="flex flex-col gap-4">
        {questions.map((question, index) => (
          <div
            key={question.id}
            className={cn('flex cursor-pointer items-center gap-2 rounded-md p-2 hover:bg-neutral-50', {
              'bg-primary-50': currentQuestionIndex === index,
            })}
            onClick={() => onSelectQuestion(index)}
          >
            <Icon
              icon={renderStatusQuestion({
                currentQuestionIndex,
                questionIndex: index,
                selectedAnswers,
                userAnswers,
                viewMode,
              })}
            />

            <Typography>{question.questionName}</Typography>
          </div>
        ))}
      </div>
    </Drawer>
  );
}

export default TestQuestionListDrawer;
