'use client';

import { Typography } from '@/components/ui';
import { ChapterSidebarProvider } from '@/context/ChapterSidebarProvider';
import { ChapterType, LessonType } from '@/modules/courses/constants/course.const';
import LearnCourseHeader from '@/modules/courses/features/learn-course/components/LearnCourseHeader';
import ChapterDrawer from '@/modules/courses/features/learn-course/components/sections-drawer/SectionsDrawer';
import TestContainer from '@/modules/courses/features/learn-course/components/test/TestContainer';
import VideoContainer from '@/modules/courses/features/learn-course/components/video/VideoContainer';
import { useCourseDetail } from '@/modules/courses/hooks';
import { AdminPreviewServices } from '@/modules/courses/services/admin.service';
import { CourseInfo, Lecture } from '@/modules/courses/types/course.type';
import { Test } from '@/modules/courses/types/test.type';

type AdminPreviewCoursePageProps = {
  courseInfo: CourseInfo;
  testDetail: Test | null;
  lectureInfo?: Lecture | null;
  isAdminPreview: boolean;
  sectionId?: string;
  lectureId?: string;
};

const AdminPreviewCoursePage = (props: AdminPreviewCoursePageProps) => {
  const { courseInfo, lectureInfo, testDetail, sectionId = '', isAdminPreview } = props;

  const courseId = courseInfo.id;

  const { courseDetailData } = useCourseDetail({
    courseId,
    queryFn: () => AdminPreviewServices.getCourse({ courseId }),
  });

  const currentSection = courseDetailData?.sections?.find((section) => section.id === sectionId);
  const isFinalTest = currentSection?.sectionType === ChapterType.Test;

  const renderCourseLearning = () => {
    if (!courseDetailData) return null;

    if (isFinalTest) {
      return (
        <TestContainer
          key={testDetail?.id}
          isAdminPreview={isAdminPreview}
          testType="finalTest"
          courseInfo={courseDetailData}
          testInfo={testDetail}
        />
      );
    }

    if (lectureInfo?.lectureType === LessonType.Video) {
      return <VideoContainer isAdminPreview={isAdminPreview} courseInfo={courseDetailData} lectureData={lectureInfo} />;
    }

    if (lectureInfo?.lectureType === LessonType.Test) {
      return (
        <TestContainer
          key={testDetail?.id}
          isAdminPreview={isAdminPreview}
          testType="quiz"
          courseInfo={courseDetailData}
          testInfo={testDetail}
        />
      );
    }

    return null;
  };

  return (
    <div className="h-screen w-full">
      <ChapterSidebarProvider>
        <div className="flex size-full flex-col">
          <LearnCourseHeader
            courseInfo={courseDetailData!}
            isAdminPreview
            courseTitle={<Typography variant="labelLg">{courseDetailData?.courseName}</Typography>}
          />

          {renderCourseLearning()}
        </div>

        <ChapterDrawer courseInfo={courseInfo} />
      </ChapterSidebarProvider>
    </div>
  );
};

export default AdminPreviewCoursePage;
