'use client';

import { Icon } from '@/components/client';
import { Button, Typography } from '@/components/ui';
import { ChapterType } from '@/modules/courses/constants/course.const';
import { CourseInfo, Lecture, Section } from '@/modules/courses/types/course.type';
import { formatApiUrl, redirectToBillingCycle } from '@/utils/url.util';
import { LikeOutlined } from '@ant-design/icons';
import { ArrowRightIcon } from '@heroicons/react/24/outline';
import { notification } from 'antd';
import { routePaths } from 'config';
import { MAX_LEARN_BASIC_COURSE } from 'constants/config';
import { CourseLevel } from 'constants/enum';
import { StarBorderOutlined } from 'icons/StarBorderOutlined';
import UserOutLined from 'icons/UserOutLined';
import Image from 'next/image';
import { useParams, useRouter } from 'next/navigation';
import queryString from 'query-string';
import { useContext, useTransition } from 'react';
import { IsCompletedCourse } from 'type';
import { notifyWarning } from 'utils/notify';
import UserInfoContext from 'utils/providers/UserInfoProvider';

export type CourseDetailOverviewProps = {
  courseInfo: CourseInfo | undefined;
};

const BarCharIcon = () => {
  return (
    <svg width="25" height="24" viewBox="0 0 25 24" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        d="M17.7878 7.2959H21.4241C21.4663 7.2959 21.5068 7.31266 21.5366 7.3425C21.5664 7.37233 21.5832 7.41279 21.5832 7.45499V21.0914C21.5832 21.1335 21.5664 21.174 21.5366 21.2038C21.5068 21.2337 21.4663 21.2504 21.4241 21.2504H17.7878C17.7456 21.2504 17.7051 21.2337 17.6753 21.2038C17.6454 21.174 17.6287 21.1335 17.6287 21.0914V7.45499C17.6287 7.41279 17.6454 7.37233 17.6753 7.3425C17.7051 7.31266 17.7456 7.2959 17.7878 7.2959Z"
        stroke="#0F1013"
        strokeWidth="1.5"
      />
      <path
        d="M10.515 2.75H14.1514C14.1936 2.75 14.2341 2.76676 14.2639 2.7966C14.2937 2.82643 14.3105 2.8669 14.3105 2.90909V21.0909C14.3105 21.1331 14.2937 21.1736 14.2639 21.2034C14.2341 21.2332 14.1936 21.25 14.1514 21.25H10.515C10.4729 21.25 10.4324 21.2332 10.4026 21.2034C10.3727 21.1736 10.356 21.1331 10.356 21.0909V2.90909C10.356 2.8669 10.3727 2.82643 10.4026 2.7966C10.4324 2.76676 10.4729 2.75 10.515 2.75Z"
        fill="#FFC41D"
        stroke="#0F1013"
        strokeWidth="1.5"
      />
      <path
        d="M7.0378 12.9091V21.0909C7.0378 21.1331 7.02104 21.1736 6.9912 21.2034C6.96137 21.2332 6.9209 21.25 6.87871 21.25H3.24234C3.20015 21.25 3.15968 21.2332 3.12985 21.2034C3.10001 21.1736 3.08325 21.1331 3.08325 21.0909V12.9091C3.08325 12.8669 3.10001 12.8264 3.12985 12.7966C3.15968 12.7668 3.20015 12.75 3.24234 12.75H6.87871C6.9209 12.75 6.96136 12.7668 6.9912 12.7966C7.02104 12.8264 7.0378 12.8669 7.0378 12.9091Z"
        stroke="#0F1013"
        strokeWidth="1.5"
      />
    </svg>
  );
};

const CourseDetailOverview = ({ courseInfo }: CourseDetailOverviewProps) => {
  const { isVipUser, isLoggedIn, userInfo, setUserInfo } = useContext(UserInfoContext);

  const params = useParams();

  const [isPending, startTransition] = useTransition();

  const router = useRouter();

  const courseLevelId = Number(courseInfo?.courseLevelId ?? 0);

  const isCompletedCourse = courseInfo?.userCourse?.[0]?.isCompleted === IsCompletedCourse.Completed;

  const onRedirectToLearner = (firstSection: Section, firstLecture?: Lecture) => {
    const courseId = params?.id?.toString();
    if (!courseId) {
      notification.warning({
        message: 'Không tìm thấy khóa học',
      });
      return;
    }

    const sectionId = firstSection.id?.toString();
    const lectureId = firstLecture?.id || '';
    const learnCourseUrl = formatApiUrl(routePaths.learner.children.lecture.path, { courseId, sectionId, lectureId });

    const queryParams = {
      lectureId,
      sectionId,
    };

    const fullUrl = queryString.stringifyUrl({
      url: routePaths.learner.path.replace(':courseId', courseId),
      query: queryParams,
    });

    startTransition(() => {
      router.push(learnCourseUrl);
    });
  };

  const isAcceptRegisterAndLearnCourse = () => {
    if (!isVipUser) return false;
    if (!userInfo || !courseInfo) {
      return false;
    }
    if (isVipUser) {
      if (courseInfo.userCourse) {
        return true;
      }
      setUserInfo({
        token: userInfo.token,
        info: {
          ...userInfo.info,
          total_course_pro_learned:
            courseLevelId === CourseLevel.Professional
              ? userInfo.info.total_course_pro_learned + 1
              : userInfo.info.total_course_pro_learned,
          total_course_basic_learned:
            courseLevelId === CourseLevel.Basic
              ? userInfo.info.total_course_basic_learned + 1
              : userInfo.info.total_course_basic_learned,
          total_course_cert_learned:
            courseLevelId === CourseLevel.Certificate
              ? userInfo.info.total_course_cert_learned + 1
              : userInfo.info.total_course_cert_learned,
        },
      });
      return true;
    }
    if (userInfo.info.total_course_basic_learned >= MAX_LEARN_BASIC_COURSE) {
      notifyWarning(
        'Tài khoản miễn phí chỉ cho phép học tối đa 3 Khóa học cơ bản. Nâng cấp khóa học để không giới hạn khóa học.',
      );
      return false;
    }
    if (
      userInfo.info.total_course_pro_learned >= MAX_LEARN_BASIC_COURSE ||
      userInfo.info.total_course_cert_learned >= MAX_LEARN_BASIC_COURSE
    ) {
      notifyWarning(
        'Tài khoản miễn phí chỉ cho phép học tối đa 3 Khóa học chuyên nghiệp. Nâng cấp khóa học để không giới hạn khóa học.',
      );
      return false;
    }
    return true;
  };

  const onStartLearn = () => {
    if (!courseInfo) return;

    const isAccept = isAcceptRegisterAndLearnCourse();
    if (!isAccept) {
      // Store current URL in localStorage for redirect after payment
      const currentUrl = typeof window !== 'undefined' ? window.location.href : '';
      redirectToBillingCycle(router, currentUrl);
      return;
    }

    const sectionsByDefault = courseInfo.sections?.filter((section) => section.sectionType === ChapterType.Default);
    const firstSection = sectionsByDefault?.[0];

    if (!firstSection) {
      notification.warning({
        message: 'Khóa học chưa có chương học và bài học',
      });
      return;
    }

    const firstLecture = firstSection?.lectures[0];
    if (!firstLecture && firstSection.lectures.length === 0 && firstSection.sectionType === ChapterType.Default) {
      notification.warning({
        message: 'Khóa học chưa có bài học',
      });
      return;
    }
    const courseId = params?.id?.toString()?.split('-').pop();
    if (!courseId) {
      notification.warning({
        message: 'Không tìm thấy khóa học',
      });
      return;
    }

    onRedirectToLearner(firstSection, firstLecture);
  };

  const onGoToRegisterPage = () => {
    router.push(routePaths.register);
  };

  const renderButtonLearner = () => {
    if (!isLoggedIn) {
      return 'Đăng ký';
    }

    if (!courseInfo?.userCourse?.length) {
      return 'Học ngay';
    }

    if (isCompletedCourse) {
      return 'Học lại';
    }

    return 'Tiếp tục học';
  };

  return (
    <div className="flex flex-col gap-4">
      <Image
        alt={courseInfo?.courseName || 'course_image'}
        className={'max-h-[160px] w-full overflow-hidden rounded-t-lg'}
        src={courseInfo?.courseThumbnailImage ?? ''}
        width={320}
        height={160}
      />

      <div className="grid w-full grid-cols-3 gap-2 px-4">
        <div className="flex flex-col items-center gap-2 rounded-md bg-neutral-50 p-2">
          <BarCharIcon />
          <Typography variant="bodyMd" className="text-ink-700">
            Trung bình
          </Typography>
        </div>

        <div className="flex flex-col items-center gap-2 rounded-md bg-neutral-50 p-2">
          <div className="flex gap-1">
            <Icon className="text-ink-black" icon={<UserOutLined />} />
            <Typography variant="labelLg" className="text-ink-black">
              {courseInfo?.totalLearner}
            </Typography>
          </div>

          <Typography variant="bodyMd" className="text-ink-700">
            Người học
          </Typography>
        </div>

        <div className="flex flex-col items-center gap-2 rounded-md bg-neutral-50 p-2">
          <div className="flex gap-1">
            <Icon className="text-ink-black" icon={<LikeOutlined style={{ fontSize: 24 }} />} />
            <Typography variant="labelLg" className="text-ink-black">
              {courseInfo?.totalRating ?? 0}
            </Typography>
          </div>

          <Typography variant="bodyMd" className="text-ink-700">
            Tích cực
          </Typography>
        </div>
      </div>

      <div className="px-6">
        <div className="flex items-center gap-2">
          <StarBorderOutlined fill={'black'} width={12} height={12} />
          <Typography variant="labelMd" className="text-ink-black">
            Học trên mọi thiết bị điện tử
          </Typography>
        </div>

        <div className="flex items-center gap-2">
          <StarBorderOutlined fill={'black'} width={12} height={12} />
          <Typography variant="labelMd" className="text-ink-black">
            Thời gian học linh hoạt
          </Typography>
        </div>

        <div className="flex items-center gap-2">
          <StarBorderOutlined fill={'black'} width={12} height={12} />
          <Typography variant="labelMd" className="text-ink-black">
            Truy cập không giới hạn
          </Typography>
        </div>
      </div>

      <Button
        className="w-full rounded-t-none"
        loading={isPending}
        size="large"
        onClick={() => (isLoggedIn ? onStartLearn() : onGoToRegisterPage())}
      >
        <div className="flex items-center gap-2">
          <Typography variant="labelLg" className="text-white">
            {renderButtonLearner()}
          </Typography>
          <Icon size="sm" icon={<ArrowRightIcon />} />
        </div>
      </Button>
    </div>
  );
};

export default CourseDetailOverview;
