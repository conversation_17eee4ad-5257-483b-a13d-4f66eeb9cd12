'use client';
import { useFilterCourse } from '@/features/courses';
import { Button, FormInstance, Modal, Space } from 'antd';

type CourseFilterModalProps = {
  open: boolean;
  form: FormInstance;
  children: React.ReactNode;
  setOpen: (open: boolean) => void;
};

const CourseFilterModal = (props: CourseFilterModalProps) => {
  const { form, children, open, setOpen } = props;

  const { handleFilter, onResetForm } = useFilterCourse({ form, openFilterModal: open, setOpenFilterModal: setOpen });

  return (
    <Modal
      className="lg:!w-[655px]"
      onCancel={() => setOpen(false)}
      title={<div className="text-2xl font-semibold">Bộ lọc</div>}
      open={open}
      footer={
        <Space className="w-full justify-between">
          <Button onClick={onResetForm} type={'text'}>
            Xóa bộ lọc
          </Button>
          <Button type={'primary'} htmlType={'submit'} onClick={handleFilter}>
            Áp dụng
          </Button>
        </Space>
      }
    >
      {children}
    </Modal>
  );
};

export default CourseFilterModal;
