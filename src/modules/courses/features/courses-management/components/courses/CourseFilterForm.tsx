'use client';
import Rating from '@/components/profileLayout/course/discovery/Rating';
import { Checkbox, Col, Divider, Form, Row } from 'antd';
import { FormInstance } from 'antd/lib';

interface CourseLevel {
  id: number;
  courseLevelName: string;
}

type Option = {
  label: string;
  value: number;
};

const courseTypeOptions: Option[] = [
  { label: 'Khóa học tiêu chuẩn', value: 1 },
  { label: 'Khóa học ngắn', value: 2 },
];

const courseLevels: CourseLevel[] = [
  { id: 1, courseLevelName: 'Cơ bản' },
  { id: 2, courseLevelName: 'Nâng cao' },
  { id: 3, courseLevelName: 'Chuyên sâu' },
  { id: 4, courseLevelName: 'Tất cả' },
];

export type CourseFilterFormProps = {
  form: FormInstance;
};

const CourseFilterForm = (props: CourseFilterFormProps) => {
  const { form } = props;

  return (
    <Form form={form} layout={'vertical'}>
      <Rating />
      <Divider />
      <Form.Item name={'levels'} label={<div className="text-2xl font-semibold">Độ khó</div>}>
        <Checkbox.Group className={'w-full'}>
          <Row className={'w-full'}>
            {courseLevels?.map((data) => (
              <Col span={6} key={data.id}>
                <Checkbox value={data.id?.toString()}>{data.courseLevelName}</Checkbox>
              </Col>
            ))}
          </Row>
        </Checkbox.Group>
      </Form.Item>
      <Divider />
      <Form.Item name={'types'} label={<div className="text-2xl font-semibold">Loại khóa học</div>}>
        <Checkbox.Group className={'w-full'}>
          <Row className={'w-full'}>
            {courseTypeOptions.map((type) => (
              <Col span={12} key={type.value}>
                <Checkbox key={type.value} value={type.value?.toString()}>
                  {type.label}
                </Checkbox>
              </Col>
            ))}
          </Row>
        </Checkbox.Group>
      </Form.Item>
      <Divider />
    </Form>
  );
};

export default CourseFilterForm;
