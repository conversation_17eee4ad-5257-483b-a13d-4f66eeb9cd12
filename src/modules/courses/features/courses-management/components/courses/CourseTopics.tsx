'use client';

import { Icon } from '@/components/client/icon';
import { Typography } from '@/components/ui';
import CourseOutLined from '@/icons/CourseOutLined';
import { cn } from '@/lib/utils';
import { Topic } from '@/modules/courses/types/course.type';
import clsx from 'clsx';
import Link from 'next/link';
import { usePathname, useSearchParams } from 'next/navigation';
import querystring from 'query-string';
import 'swiper/css';
import 'swiper/css/navigation';

export type CourseTopicsProps = {
  topics: Topic[];
};

const getTopicIcon = (topic: Topic, topicId: string) => {
  const isAllTopic = !topic.id;

  const icon = topic.icon;

  if (isAllTopic) {
    return <CourseOutLined color={topicId === topic.id ? clsx('text-primary') : ''} />;
  }

  return <Icon icon={icon} className="text-primary" />;
};

function CourseTopics(props: CourseTopicsProps) {
  const { topics } = props;

  const params = useSearchParams();

  const pathname = usePathname();

  const topicId = params.get('topic') ?? '';

  return (
    <div className="flex w-full gap-12">
      <div className="flex w-full flex-wrap gap-2">
        {topics.splice(0, 8).map((topic) => {
          const topicIcon = getTopicIcon(topic, topicId);

          return (
            <Link
              key={topic.id}
              className={cn(
                'rounded-full bg-neutral-50 px-4 py-2.5',
                topicId === topic.id ? 'border-2 border-primary bg-primary-50 text-primary' : 'text-secondary_text',
              )}
              scroll={false}
              href={querystring.stringifyUrl({
                url: pathname,
                query: { ...querystring.parse(params.toString()), topic: topic.id, page: 1 },
              })}
            >
              <div className="flex cursor-pointer justify-center hover:text-primary">
                <div className="flex items-center gap-2">
                  {topicIcon}

                  <Typography
                    variant="labelMd"
                    className={topicId === topic.id ? 'text-primary' : 'text-secondary_text'}
                  >
                    {topic.topicName}
                  </Typography>
                </div>
              </div>
            </Link>
          );
        })}
      </div>
    </div>
  );
}

export default CourseTopics;
