'use client';

import { Pagination } from '@/components/ui';
import { useSafeSearchParams } from '@/hooks';
import { CoursesByFilterParams } from '@/modules/courses/types/course.type';
import { usePathname, useRouter } from 'next/navigation';
import queryString from 'query-string';

const INITIAL_LIMIT = 16;

const useCoursePagination = () => {
  const router = useRouter();
  const pathname = usePathname();

  const { parsedQueryParams } = useSafeSearchParams<CoursesByFilterParams>();

  const onChangePage = (page: number, pageSize: number) => {
    const params = { ...parsedQueryParams, page: page, limit: pageSize };
    router.push(queryString.stringifyUrl({ url: pathname, query: { ...params } }));
  };

  return { onChangePage, parsedQueryParams };
};

const CoursesPagination = ({ total }: { total: number }) => {
  const { onChangePage } = useCoursePagination();
  const { parsedQueryParams } = useSafeSearchParams<CoursesByFilterParams>();

  return (
    <div className="flex items-center justify-center">
      <Pagination
        total={total}
        showSizeChanger={false}
        current={parsedQueryParams.page ? parsedQueryParams.page : 1}
        pageSize={INITIAL_LIMIT}
        onChange={onChangePage}
      />
    </div>
  );
};

export default CoursesPagination;
