'use client';
import { Icon } from '@/components/client';
import { Button, Typography } from '@/components/ui';
import { routePaths } from '@/config';
import { transformSecondToHour } from '@/constants/time';
import { CourseItem } from '@/modules/courses/';
import CourseOverview from '@/modules/courses/components/course-item/CourseOverview';
import CourseRating from '@/modules/courses/components/course-item/CourseRating';
import { useFavoriteCourse } from '@/modules/courses/hooks';
import { CourseInfo, Topic } from '@/modules/courses/types/course.type';
import { AdjustmentsHorizontalIcon } from '@heroicons/react/24/outline';
import { Col, Form, Row, Space } from 'antd';
import { useState } from 'react';
import CourseFilterForm from './CourseFilterForm';
import CourseFilterModal from './CourseFilterModal';
import CoursesPagination from './CoursePagination';
import CourseTopics from './CourseTopics';

const Courses = ({
  coursesData,
  isLoggedIn,
  topics = [],
  showFilter = true,
}: {
  topics?: Topic[];
  coursesData: { data: CourseInfo[]; count: number };
  isLoggedIn: boolean;
  showFilter?: boolean;
}) => {
  const { count: total, data: courses } = coursesData;

  const collapsed = true;

  const { onFavorite } = useFavoriteCourse();

  const [form] = Form.useForm();

  const [openFilterModal, setOpenFilterModal] = useState(false);

  const allTopics = [{ id: '', topicName: 'Tất cả khóa học' }].concat(topics) as Topic[];

  return (
    <div className="bg-white">
      <Space className="w-full" direction={'vertical'} size={24}>
        {showFilter && (
          <>
            <Typography variant="headlineSm">Tất cả khóa học</Typography>
            <div className="flex gap-12">
              <div style={{ width: `calc(100vw - ${collapsed ? '435' : '734'}px)` }}>
                <CourseTopics topics={allTopics} />
              </div>

              <div>
                <Button
                  size="small"
                  variant="tertiary"
                  className="size-fit bg-neutral-50"
                  onClick={() => setOpenFilterModal(true)}
                >
                  <Icon icon={<AdjustmentsHorizontalIcon className="text-ink-black" />} />
                </Button>
              </div>
            </div>
          </>
        )}
        <Row gutter={[24, 24]}>
          {courses?.length > 0 ? (
            courses.map((item) => (
              <Col key={item.id} md={8} lg={8} xxl={6} span={24}>
                <CourseItem
                  courseData={{
                    courseId: item.id,
                    courseName: item.courseName,
                    courseThumbnail: item.courseThumbnailImage,
                    courseUrl: routePaths.profile.children.course.children.detail.path.replace(':id', item.id),
                  }}
                  canFavorite
                  creatorName={item.createdBy.name}
                  isFavorite={item.isFavorite}
                  topicName={item.topic.topicName}
                  footer={
                    <div className="flex flex-col gap-3">
                      <CourseRating rating={Number(item.avgRating) || 0} reviewCount={Number(item.totalRating) || 0} />
                      <CourseOverview
                        sectionsCount={Number(item.totalSections) || 0}
                        duration={transformSecondToHour(Number(item.courseDuration)) ?? 0}
                      />
                    </div>
                  }
                  onFavorite={(isFavorite) => onFavorite({ courseId: item.id, isFavorite })}
                />
              </Col>
            ))
          ) : (
            <div className="flex h-40 w-full items-center justify-center">
              <Typography variant="titleMd">Không tìm thấy khóa học phù hợp với điều kiện</Typography>
            </div>
          )}
        </Row>

        {courses.length > 0 && <CoursesPagination total={total} />}
      </Space>

      {openFilterModal && (
        <CourseFilterModal open={openFilterModal} setOpen={setOpenFilterModal} form={form}>
          <CourseFilterForm form={form} />
        </CourseFilterModal>
      )}
    </div>
  );
};

export default Courses;
