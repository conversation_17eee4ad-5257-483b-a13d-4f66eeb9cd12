'use client';

import { Typography } from '@/components/ui';
import { routePaths } from '@/config';
import CourseItem from '@/modules/courses/components/course-item/CourseItem';
import CourseOverview from '@/modules/courses/components/course-item/CourseOverview';
import CourseRating from '@/modules/courses/components/course-item/CourseRating';
import { useFavoriteCourse } from '@/modules/courses/hooks';
import { CourseInfo } from '@/modules/courses/types/course.type';
import { useRouter } from 'next/navigation';
import EmptyCourses from './EmptyCourses';

interface FavoriteCoursesProps {
  courses: CourseInfo[];
}

const CourseGrid = ({ courses }: FavoriteCoursesProps) => {
  const { onFavorite } = useFavoriteCourse();

  return (
    <div className="grid h-full grid-cols-2 gap-6 sm:grid-cols-3 md:grid-cols-4">
      {courses.map((course) => (
        <CourseItem
          key={course.id}
          onFavorite={(isFavorite) => onFavorite({ courseId: course.id, isFavorite })}
          isFavorite={course.isFavorite}
          creatorName={course.createdBy.name}
          courseData={{
            courseId: course.id,
            courseName: course.courseName,
            courseThumbnail: course.courseThumbnailImage,
            courseUrl: routePaths.profile.children.course.children.detail.path.replace(':id', course.id),
          }}
          topicName={course.topic.topicName}
          footer={
            <div className="flex flex-col gap-3">
              <CourseRating rating={Number(course.avgRating) || 0} reviewCount={Number(course.totalRating) || 0} />
              <CourseOverview
                sectionsCount={Number(course.totalSections) || 0}
                duration={Number(course.duration) || 0}
              />
            </div>
          }
        />
      ))}
    </div>
  );
};

const FavoriteCourses: React.FC<FavoriteCoursesProps> = ({ courses }) => {
  const router = useRouter();

  const handleViewAllCourses = () => {
    router.push(routePaths.profile.children.course.children.discovery.path);
  };

  return (
    <div className="flex flex-col gap-4">
      <Typography variant="headlineSm">Khóa học yêu thích</Typography>

      {courses.length ? (
        <CourseGrid courses={courses} />
      ) : (
        <EmptyCourses
          description="Bạn chưa thêm khoá học nào vào danh sách yêu thích"
          buttonTitle="Xem tất cả khóa học"
          onClick={handleViewAllCourses}
        />
      )}
    </div>
  );
};

export default FavoriteCourses;
