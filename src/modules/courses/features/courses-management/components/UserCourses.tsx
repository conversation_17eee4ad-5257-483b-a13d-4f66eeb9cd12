'use client';

import { Tabs, Typography } from '@/components/ui';
import CourseItem from '@/modules/courses/components/course-item/CourseItem';
import CourseProgress from '@/modules/courses/components/course-item/CourseProgress';
import EmptyCourses from '@/modules/courses/features/courses-management/components/EmptyCourses';
import React from 'react';
import { UserCourse } from '../../../types/course.type';
import {
  calculateLearningPercent,
  getCompletedCourses,
  getCourseUrlByStatus,
  getInProgressCourses,
  getLatestCourse,
} from '../utils/course.util';
import LatestCourseBanner from './LatestCourseBanner';

const TAB = {
  InProgress: 'in_progress',
  Completed: 'completed',
};

function CourseList({ userCourses }: { userCourses: UserCourse[] }) {
  return (
    <div className="grid grid-cols-2 gap-6 sm:grid-cols-3 md:grid-cols-4">
      {userCourses.map((course) => {
        const learningProgressPercent = calculateLearningPercent(course);
        const courseUrl = getCourseUrlByStatus({ courseData: course });

        return (
          <CourseItem
            creatorName={course.course.createdBy.name}
            courseData={{
              courseId: course.course.id,
              courseName: course.course.courseName,
              courseThumbnail: course.course.courseThumbnailImage,
              courseUrl,
            }}
            key={course.id}
            footer={<CourseProgress progressPercent={learningProgressPercent} />}
          />
        );
      })}
    </div>
  );
}

export default function UserCourses({ userCourses }: { userCourses: UserCourse[] }) {
  const [tab, setTab] = React.useState(TAB.InProgress);

  const latestCourse = getLatestCourse(userCourses);

  const inProgressCourses = getInProgressCourses(userCourses);
  const completedCourses = getCompletedCourses(userCourses);

  const userCoursesByStatus = tab === TAB.InProgress ? inProgressCourses : completedCourses;

  return (
    <div className="flex flex-col gap-4">
      <Typography variant="headlineSm">Khóa học của tôi</Typography>

      <Tabs
        activeKey={tab}
        defaultActiveKey={tab}
        onChange={(key) => setTab(key)}
        items={[
          {
            label: 'Đang diễn ra',
            key: TAB.InProgress,
          },
          { label: 'Đã hoàn thành', key: TAB.Completed },
        ]}
      />

      {tab === TAB.InProgress && <LatestCourseBanner latestCourse={latestCourse} />}

      {tab === TAB.Completed && completedCourses.length === 0 && (
        <EmptyCourses
          description="Hoàn thành khoá học để lưu lại thành tích tại đây!"
          buttonTitle="Đi đến Khoá học đang diễn ra"
          onClick={() => setTab(TAB.InProgress)}
        />
      )}

      <CourseList userCourses={userCoursesByStatus} />
    </div>
  );
}
