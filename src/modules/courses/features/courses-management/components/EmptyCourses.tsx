import { Button, Typography } from '@/components/ui';

interface EmptyCoursesProps {
  description: string;
  buttonTitle: string;
  onClick: () => void;
}

const EmptyCourses = (props: EmptyCoursesProps) => {
  const { description, buttonTitle, onClick } = props;
  return (
    <div className="flex h-40 w-full flex-col items-center justify-center gap-3">
      <Typography variant="titleMd">{description}</Typography>

      <div>
        <Button variant="secondary" onClick={onClick}>
          {buttonTitle}
        </Button>
      </div>
    </div>
  );
};

export default EmptyCourses;
