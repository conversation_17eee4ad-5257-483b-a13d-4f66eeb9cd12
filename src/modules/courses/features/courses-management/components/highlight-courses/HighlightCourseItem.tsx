'use client';
import { Icon } from '@/components/client';
import { Button, Typography } from '@/components/ui';
import { StarIcon } from '@/icons';
import { convertSecondsToHour } from '@/lib/dateTime';
import { cn } from '@/lib/utils';
import { CourseInfo } from '@/modules/courses/types/course.type';
import { StarIcon as HStarIcon } from '@heroicons/react/20/solid';
import { routePaths } from 'config';
import Image from 'next/image';
import Link from 'next/link';
import { useUserInfoProvider } from 'utils/providers/UserInfoProvider';

import { StarIcon as HStarIconOutline } from '@heroicons/react/24/outline';
import { StarIcon as HStarIconSolid } from '@heroicons/react/24/solid';

export type CourseHighlightItemProps = {
  courseData: CourseInfo;

  onFavorite: () => void;
};

function HighlightCourseItem(props: CourseHighlightItemProps) {
  const { courseData, onFavorite } = props;

  const {
    isFavorite,
    courseName,
    courseThumbnailImage,
    id,

    totalSections,
    courseDuration,

    avgRating,
    topic,
  } = courseData;

  const totalRating = Number(courseData.totalRating) ?? 0;

  const { isLoggedIn } = useUserInfoProvider();

  return (
    <div className="flex size-full flex-col rounded-xl">
      {isLoggedIn && (
        <Button
          variant="ghost-reversed"
          className={'absolute right-2.5 top-2.5 z-10 size-8 border border-white bg-transparent'}
          onClick={onFavorite}
          icon={
            isFavorite ? (
              <HStarIconSolid className={cn('size-5 text-yellow-500')} />
            ) : (
              <HStarIconOutline className={cn('size-5 text-white')} />
            )
          }
        />
      )}

      <Image fill alt="highlight-course-image" className="w-full rounded-xl object-cover" src={courseThumbnailImage} />

      <div className="absolute bottom-0 flex w-full justify-between rounded-xl bg-gradient-to-t from-[#070418] to-transparent p-4">
        <div className="flex flex-col gap-2">
          <div className="flex flex-col gap-1">
            <Typography variant="labelSm" className="uppercase text-secondary_text_reversed">
              {topic?.topicName}
            </Typography>

            <Typography variant="headlineXs" className="text-ink-white">
              {courseName}
            </Typography>
          </div>

          <Typography variant="labelSm" className="text-ink-white">
            {courseData.createdBy.name}
          </Typography>

          <div className="flex items-center gap-2 text-ink-white">
            <Typography variant="labelSm" className="text-ink-white">
              {totalSections} chương
            </Typography>

            <Icon className="size-2" icon={<StarIcon />} />

            <Typography variant="labelSm" className="text-ink-white">
              {convertSecondsToHour(Number(courseDuration ?? 0))} giờ học
            </Typography>
            <Icon className="size-2" icon={<StarIcon />} />

            <div className="flex items-center gap-1">
              <Icon className="size-3" icon={<HStarIcon className="text-yellow-500" />} />

              <Typography variant="labelSm" className="text-ink-white">
                {avgRating}
              </Typography>

              <Typography variant="labelSm" className="text-ink-white">
                ({totalRating} reviews)
              </Typography>
            </div>
          </div>
        </div>

        <Link href={`${routePaths.profile.children.course.path}/${id}`} className="self-end">
          <Button
            className={
              'w-32 border-secondary-400 bg-secondary-400 text-ink-black hover:bg-secondary-300 active:bg-secondary-500'
            }
            size="small"
          >
            <Typography variant="labelLg">Học ngay</Typography>
          </Button>
        </Link>
      </div>
    </div>
  );
}

export default HighlightCourseItem;
