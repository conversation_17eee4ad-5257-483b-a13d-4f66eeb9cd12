'use client';
import Line from '@/components/profileLayout/course/discovery/Line';
import { Typography } from '@/components/ui';
import { useClient } from '@/hooks/useClient';
import { useFavoriteCourse } from '@/modules/courses/hooks';
import { CourseInfo } from '@/modules/courses/types/course.type';
import { Carousel } from 'antd';
import { CarouselRef } from 'antd/lib/carousel';
import { StarEightEdgeOutlined, StarFourEdgeOutlined, UnderlineOutlined } from 'icons';
import React, { ReactNode } from 'react';
import HighlightCourseItem from './HighlightCourseItem';

export type CourseHighlightTabProps = {
  title?: string | ReactNode;
  courses: CourseInfo[] | null | undefined;
};

function HighlightCoursesBanner({ title = 'Khóa học nổi bật', courses }: Readonly<CourseHighlightTabProps>) {
  const carouselRef = React.useRef<CarouselRef>(null);

  const isClient = useClient();

  const { onFavorite } = useFavoriteCourse();

  if (!courses) return null;

  return (
    <div className="relative bg-primary pb-16">
      <span className={'absolute left-0 top-0 hidden lg:block'}>
        <Line />
      </span>
      <div className="grid grid-cols-3 gap-4 p-6">
        <StarEightEdgeOutlined />
        <div className="flex items-end justify-center text-2xl font-bold text-white lg:pt-[30px]">
          <div className={'flex flex-col gap-0'}>
            <Typography variant="headlineSm" className="text-ink-white">
              {title}
            </Typography>

            <UnderlineOutlined />
          </div>
        </div>
        <div className="flex justify-end">
          <StarFourEdgeOutlined />
        </div>
      </div>

      <div className={'min-h-[270px] md:min-h-[382px]'}>
        {isClient && (
          <Carousel
            className="center"
            infinite
            dots
            variableWidth
            responsive={[
              {
                breakpoint: 600,
                settings: {
                  slidesToShow: 1,
                  centerPadding: '0px',
                  variableWidth: false,
                  centerMode: false,
                  adaptiveHeight: false,
                  dots: false,
                },
              },
            ]}
            centerPadding={'60px'}
            adaptiveHeight
            centerMode
            speed={1000}
            ref={carouselRef}
            swipeToSlide
            autoplay
            appendDots={(dots) => {
              return (
                <div
                  style={{
                    borderRadius: '10px',
                    padding: '10px',
                    position: 'absolute',
                    bottom: '-40px',
                  }}
                >
                  <ul style={{ margin: '0px' }}> {dots} </ul>
                </div>
              );
            }}
          >
            {courses.map((course) => {
              return (
                <div
                  key={course.id}
                  className="scaleItem relative h-[270px] w-full overflow-hidden md:m-[56px] md:w-[480px]"
                >
                  <HighlightCourseItem
                    courseData={course}
                    onFavorite={() => onFavorite({ courseId: course.id, isFavorite: !course.isFavorite })}
                  />
                </div>
              );
            })}
          </Carousel>
        )}
      </div>
    </div>
  );
}

export default HighlightCoursesBanner;
