import { routePaths } from '@/config';
import { Lecture, UserCourse } from '@/modules/courses/types/course.type';
import { formatApiUrl } from '@/utils/url.util';

export const getLatestCourse = (userCourses: UserCourse[]) => {
  const inProgressCourses = getInProgressCourses(userCourses);
  if (inProgressCourses.length === 0) return null;

  return inProgressCourses?.[0] ?? null;
};

export const getLatestSection = ({
  latestCourse,
  latestSectionId,
}: {
  latestSectionId: string;
  latestCourse: UserCourse;
}) => {
  if (!latestCourse) return null;

  return latestCourse?.course.sections.find((section) => section.id === latestSectionId);
};

export const getLatestLecture = (lectures: Lecture[], latestCourse: UserCourse) => {
  if (!latestCourse) return null;

  return lectures?.find((lecture) => lecture.id === latestCourse?.lastViewLecture?.id);
};

export const calculateLearningPercent = (latestCourse: UserCourse) => {
  return Math.floor((latestCourse?.countCompletedLectures / latestCourse?.totalLectures) * 100);
};

export const getCompletedCourses = (userCourses: UserCourse[]) => {
  return userCourses.filter((item) => item.isCompleted === 1);
};

export const getInProgressCourses = (userCourses: UserCourse[]) => {
  return userCourses.filter((item) => item.isCompleted === 0);
};

export const getCourseUrlByStatus = ({ courseData }: { courseData: UserCourse }) => {
  const courseId = courseData.course.id;
  const sectionId = courseData?.lastViewLecture?.sectionId;
  const lectureId = courseData?.lastViewLecture?.id;

  if (!sectionId || !lectureId) return '';

  const COMPLETED = 1;
  if (courseData.isCompleted === COMPLETED) {
    return formatApiUrl(routePaths.profile.children.course.children.detail.path, { id: courseId });
  }

  const courseUrl = formatApiUrl(routePaths.learner.children.lecture.path, { courseId, sectionId, lectureId });
  return courseUrl;
};
