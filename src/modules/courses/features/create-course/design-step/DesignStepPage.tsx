import { handleAsync } from '@/lib/handle-async';
import CreatorTutorialService from '@/modules/courses/features/create-course/design-step/services/creator-tutorial.service';
import { CourseInfo } from '@/modules/courses/types/course.type';
import { getCourseByIdService } from '../../../services/course.service';
import DesignStepContainer from './DesignStepContainer';

const DesignStepPage = async ({ params }: { params: { courseId: string } }) => {
  const [_, courseDetail] = await handleAsync<CourseInfo | null>(getCourseByIdService(params.courseId));

  if (!courseDetail) {
    return <div>Không tìm thấy khóa học</div>;
  }

  const sections = courseDetail.sections || [];
  const shouldCreateInitialData = sections.length === 0 && (await CreatorTutorialService.shouldCreateTutorial());
  if (shouldCreateInitialData) {
    await handleAsync(CreatorTutorialService.createInitialData({ courseId: params.courseId }));
  }

  return <DesignStepContainer courseDetail={courseDetail} />;
};

export default DesignStepPage;
