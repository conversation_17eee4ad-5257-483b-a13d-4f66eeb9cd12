import { CourseInfo } from '@/modules/courses/types/course.type';
import React from 'react';

type DesignStepContextValue = { courseDetail: CourseInfo | null };

const DesignStepContext = React.createContext<DesignStepContextValue | null>(null);

const DesignStepProvider = ({ children, value }: { children: React.ReactNode; value: DesignStepContextValue }) => {
  return <DesignStepContext.Provider value={value}>{children}</DesignStepContext.Provider>;
};

export const useDesignStepProvider = () => {
  const context = React.useContext(DesignStepContext);
  if (!context) {
    throw new Error('useDesignStepProvider must be used within a DesignStepProvider');
  }
  return context as DesignStepContextValue;
};

export default DesignStepProvider;
