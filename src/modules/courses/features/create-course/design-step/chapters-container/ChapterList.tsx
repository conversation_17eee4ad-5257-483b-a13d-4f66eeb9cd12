'use client';

import { Icon } from '@/components/client';
import { Button, Dropdown, Typography } from '@/components/ui';
import { secondsToHHMMSSTextFormat } from '@/lib/dateTime';
import { useChaptersProvider } from '@/modules/courses/features/create-course/design-step/chapters-container/ChaptersProvider';
import TestItem from '@/modules/courses/features/create-course/design-step/chapters-container/TestItem';
import useSectionActions from '@/modules/courses/features/create-course/design-step/hooks/useSectionActions';
import { useCourseEditable } from '@/modules/courses/hooks';
import { CourseInfo } from '@/modules/courses/types/course.type';
import { swapElements } from '@/utils';
import { DragDropContext, Draggable, Droppable, DropResult } from '@hello-pangea/dnd';
import { PlusIcon } from '@heroicons/react/24/outline';
import { useParams } from 'next/navigation';
import { ChapterType } from '../../../../constants/course.const';
import EmptyChapterData from '../components/EmptyContent';
import { createChapterSortIndex, createDraftSection, createSection } from '../utils/section.util';
import ChapterItem from './ChapterItem';

function CourseSummary({ totalLessons = 0, totalDuration = 0 }: { totalLessons: number; totalDuration: number }) {
  return (
    <div className="flex w-fit gap-4 rounded-lg border-neutral-100 bg-white px-4 py-2">
      <div className="flex items-center gap-2">
        <Typography variant="titleSm">Tổng số chương:</Typography>
        <Typography variant="titleSm">{totalLessons}</Typography>
      </div>
      <div className="border-l-2 border-neutral-200" />
      <div className="flex items-center gap-2">
        <Typography variant="titleSm">Tổng thời lượng video:</Typography>
        <Typography variant="titleSm">{secondsToHHMMSSTextFormat(totalDuration)}</Typography>
      </div>
    </div>
  );
}

function AddContentButton({
  isFinalTestExist,
  onCreate,
}: {
  isFinalTestExist: boolean;
  onCreate: (type: ChapterType) => void;
}) {
  return (
    <div>
      <Dropdown
        trigger={['click']}
        menu={{
          items: [
            {
              key: 'add-chapter',
              label: 'Thêm chương',
              onClick: () => {
                onCreate?.(ChapterType.Default);
              },
            },
            {
              key: 'add-quiz',
              label: 'Thêm bài kiểm tra',
              onClick: () => {
                onCreate?.(ChapterType.Test);
              },
              disabled: isFinalTestExist,
            },
          ],
        }}
      >
        <Button className="step-2">
          <div className="flex gap-2">
            <Icon icon={<PlusIcon />} />
            <Typography variant="labelLg" className="text-white">
              Thêm nội dung
            </Typography>
          </div>
        </Button>
      </Dropdown>
    </div>
  );
}

type ChapterList = {
  courseDetail: CourseInfo;
};

export default function ChapterList(props: ChapterList) {
  const { courseDetail } = props;

  const { courseId } = useParams<{ courseId: string }>();

  const { sections, isCreatingDraft, setIsCreatingDraft, setSections } = useChaptersProvider();

  const { onSwapSection, onCreateSection } = useSectionActions();

  const { canEditCourse, showCannotEditModal, contextHolderModal } = useCourseEditable();

  const hasChapter = !!sections.length;
  const courseName = courseDetail?.courseName;

  const sortedSections = sections.toSorted((a, b) => a.sortIndex - b.sortIndex);

  const isFinalTestExist = sections.some((section) => section.sectionType === ChapterType.Test);

  const handleCreateFinalTest = () => {
    if (isFinalTestExist) return;

    const sortIndex = createChapterSortIndex(sections, ChapterType.Test);
    const newSection = createSection({
      sortIndex,
      sectionType: ChapterType.Test,
      sectionName: 'Bài kiểm tra cuối khóa',
    });

    onCreateSection({
      courseId,
      sectionName: newSection.sectionName,
      sectionType: newSection.sectionType,
      sortIndex: newSection.sortIndex,
    });
  };

  const handleCreateDraftSection = (type: ChapterType) => {
    if (isCreatingDraft) return;
    setIsCreatingDraft(true);

    const sortIndex = createChapterSortIndex(sections, type);
    const newSection = createDraftSection({ sortIndex, sectionType: type });
    setSections([...sections, newSection]);
  };

  const handleAddSection = (type: ChapterType) => {
    if (!canEditCourse(courseDetail)) {
      showCannotEditModal();
      return;
    }

    if (type === ChapterType.Default) {
      handleCreateDraftSection(type);
      return;
    }

    handleCreateFinalTest();
  };

  const handleSwapSection = (result: DropResult) => {
    if (!canEditCourse(courseDetail)) {
      showCannotEditModal();
      return;
    }

    const { source, destination } = result;
    if (!destination) return;

    const newChapters = swapElements(sections, source.index, destination.index, 'sortIndex');
    setSections(newChapters);

    onSwapSection({
      courseId,
      sectionId: newChapters[source.index].id,
      sourceIndex: source.index,
      destinationIndex: destination.index,
    });
  };

  return (
    <div className="flex size-full flex-col gap-4 overflow-y-auto px-32 py-10">
      <div className="flex items-end justify-between">
        <div className="flex flex-col gap-2">
          <Typography variant="labelMd" className="text-secondary_text">
            {courseName}
          </Typography>
          <Typography variant="headlineSm">Danh sách Chương bài học</Typography>
        </div>

        <AddContentButton isFinalTestExist={isFinalTestExist} onCreate={handleAddSection} />
      </div>

      {hasChapter && (
        <CourseSummary
          totalLessons={Number(courseDetail.totalSections || 0)}
          totalDuration={Number(courseDetail.courseDuration || 0)}
        />
      )}

      <div>
        {sections.length ? (
          <div className="step-3 flex size-full flex-col gap-4">
            <DragDropContext onDragEnd={handleSwapSection}>
              <Droppable droppableId="chapters" isDropDisabled={isCreatingDraft}>
                {(provided) => (
                  <div ref={provided.innerRef} {...provided.droppableProps} className="flex flex-col gap-4">
                    {sortedSections.map((chapter, index) => {
                      if (chapter.sectionType === ChapterType.Test) return null;
                      const isDraft = chapter.id === 'draft';

                      if (isDraft) {
                        return (
                          <ChapterItem
                            courseDetail={courseDetail}
                            key={chapter.id}
                            sectionType={ChapterType.Default}
                            isCreatingDraft={true}
                            setIsCreatingDraft={setIsCreatingDraft}
                            section={chapter}
                            isDragging={false}
                          />
                        );
                      }

                      return (
                        <Draggable key={chapter.id + index} draggableId={`chapter-${chapter.id}`} index={index}>
                          {(provided, snapshot) => (
                            <div ref={provided.innerRef} {...provided.draggableProps} {...provided.dragHandleProps}>
                              <ChapterItem
                                courseDetail={courseDetail}
                                sectionType={ChapterType.Default}
                                isCreatingDraft={false}
                                setIsCreatingDraft={setIsCreatingDraft}
                                section={chapter}
                                isDragging={snapshot.isDragging}
                              />
                            </div>
                          )}
                        </Draggable>
                      );
                    })}
                    {provided.placeholder}
                  </div>
                )}
              </Droppable>
            </DragDropContext>

            {sections.map((chapter) => {
              if (chapter.sectionType === ChapterType.Test) {
                return <TestItem key={chapter.id} section={chapter} />;
              }

              return null;
            })}
          </div>
        ) : (
          <EmptyChapterData />
        )}
      </div>
      {contextHolderModal}
    </div>
  );
}
