import { ChapterType, LessonType } from '@/modules/courses/constants/course.const';
import { createLessonService, createSectionService } from '@/modules/courses/services/course.service';
import { userServices } from '@/services/user.services';

interface CreateTutorialParams {
  courseId: string;
}

class CreatorTutorialService {
  static async shouldCreateTutorial(): Promise<boolean> {
    const { userInfo } = await userServices();
    return Boolean(userInfo?.info?.id && !userInfo?.info?.has_completed_creator_tutorial);
  }

  static async createInitialData({ courseId }: CreateTutorialParams): Promise<void> {
    const initialSection = await createSectionService({
      courseId,
      sectionName: 'Chương 1',
      sortIndex: 1,
      sectionType: ChapterType.Default,
    });

    const initialLessons = [
      {
        courseId,
        sectionId: initialSection.id,
        lectureName: 'Bài học 1',
        lectureType: LessonType.Video,
        sortIndex: 1,
      },
      {
        courseId,
        sectionId: initialSection.id,
        lectureName: 'Bài kiểm tra 1',
        lectureType: LessonType.Test,
        sortIndex: 2,
      },
    ];

    await Promise.all(initialLessons.map((lesson) => createLessonService(lesson)));
  }
}

export default CreatorTutorialService;
