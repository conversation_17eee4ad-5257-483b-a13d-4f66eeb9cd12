'use client';

import { routePaths } from '@/config';
import { HttpStatusCode } from '@/constants/api';
import { QUERY_KEYS } from '@/constants/query-keys';
import { usePreventHydration } from '@/hooks';
import useCompleteCreatorTutorial from '@/hooks/useCompleteCreatorTutorial';
import useSafeSearchParams from '@/hooks/useSafeSearchParams';
import FinalTestContainer from '@/modules/courses/features/create-course/design-step/final-test/FinalTestContainer';
import { getCourseByIdService } from '@/modules/courses/services/course.service';
import { CourseInfo, Section } from '@/modules/courses/types/course.type';
import { UserInfo } from '@/type';
import { useUserInfoProvider } from '@/utils/providers/UserInfoProvider';
import { formatApiUrl } from '@/utils/url.util';
import { useRouter } from 'next-nprogress-bar';
import dynamic from 'next/dynamic';
import { useParams } from 'next/navigation';
import React, { useEffect, useState } from 'react';
import Joyride, { CallBackProps, STATUS, Step } from 'react-joyride';
import { useQuery } from 'react-query';
import CreateCourseHeader from '../components/header/Header';
import ChaptersSkeleton from './chapters-container/ChaptersSkeleton';
import Sidebar from './components/Sidebar';
import DesignStepProvider from './DesignStepProvider';
import LessonSkeleton from './lesson-detail/LessonSkeleton';

const LessonContainer = dynamic(() => import('./lesson-detail/LessonContainer'), {
  ssr: false,
  loading: () => <LessonSkeleton />,
});

const ChaptersContainer = dynamic(() => import('./chapters-container/ChaptersContainer'), {
  ssr: false,
  loading: () => (
    <div className="flex items-center justify-center px-32">
      <ChaptersSkeleton />
    </div>
  ),
});

const QuizContainer = dynamic(() => import('./quiz/QuizContainer'), {
  ssr: false,
  loading: () => <LessonSkeleton />,
});

const steps: Step[] = [
  {
    target: '.step-1',
    disableBeacon: true,
    content: (
      <div>
        <h3 className="text-[16px] font-medium text-white">Quản lý cấu trúc khóa học</h3>
        <br />
        <p className="font-nunito text-[12px] text-white">
          Đây là nơi bạn quản lý toàn bộ cấu trúc khóa học - thêm, sắp xếp và chỉnh sửa chương & bài học một cách dễ
          dàng.
        </p>
      </div>
    ),
    title: '1/5',
    placement: 'right' as const,
  },
  {
    target: '.step-2',
    disableBeacon: true,
    content: (
      <div>
        <h3 className="text-[16px] font-medium text-white">Thêm chương và bài kiểm tra cuối khoá học</h3>
        <br />
        <p className="font-nunito text-[12px] text-white">
          Lưu ý: Mỗi khóa học chỉ có thể có 1 bài kiểm tra duy nhất, luôn nằm cuối danh sách chương.
        </p>
      </div>
    ),
    title: '2/5',
    placement: 'bottom' as const,
  },
  {
    target: '.step-3',
    disableBeacon: true,
    content: (
      <div>
        <h3 className="text-[16px] font-medium text-white">Mẫu cấu trúc khoá học cơ bản</h3>
        <br />
        <p className="font-nunito text-[12px] text-white">
          Studify đã tạo sẵn một mẫu cấu trúc cơ bản để bạn dễ hình dung và bắt đầu thiết kế nội dung nhanh chóng.
        </p>
      </div>
    ),
    title: '3/5',
    placement: 'top' as const,
  },
  {
    target: '.step-4',
    disableBeacon: true,
    content: (
      <div>
        <h3 className="text-[16px] font-medium text-white">Muốn thay đổi nội dung?</h3>
        <br />
        <p className="font-nunito text-[12px] text-white">
          Chỉ cần bấm trực tiếp vào dòng chữ để chỉnh sửa ngay tại chỗ.
        </p>
      </div>
    ),
    title: '4/5',
    placement: 'bottom-start' as const,
  },
  {
    target: '.step-5',
    disableBeacon: true,
    content: (
      <div>
        <h3 className="text-[16px] font-medium text-white">
          Cấu trúc khóa học luôn được cập nhật và hiển thị ở thanh bên trái.
        </h3>
        <br />
        <p className="font-nunito text-[12px] text-white">
          Bấm vào tên bài giảng để bắt đầu thiết kế nội dung cho từng bài
        </p>
      </div>
    ),
    title: '5/5',
    placement: 'right' as const,
  },
];

type Props = {
  courseDetail: CourseInfo;
};

const useChapters = ({ courseDetail }: { courseDetail: CourseInfo | null }) => {
  const [sections, setSections] = React.useState<Section[]>(courseDetail?.sections || []);

  React.useEffect(() => {
    setSections(courseDetail?.sections || []);
  }, [courseDetail]);

  return { sections, setSections };
};

export const useCourseDetail = ({ courseDetail }: { courseDetail: CourseInfo | null }) => {
  const params = useParams<{ courseId: string }>();

  const { data: courseDetailData } = useQuery({
    queryKey: [QUERY_KEYS.COURSE_DETAIL, params.courseId],
    enabled: !!courseDetail,
    initialData: courseDetail,
    queryFn: () => getCourseByIdService(params.courseId),
  });

  return courseDetailData;
};

const useTutorial = (userInfo: UserInfo | undefined, onCompleteTutorial?: () => void) => {
  const [showTutorial, setShowTutorial] = useState(false);
  const [runTour, setRunTour] = useState(false);

  useEffect(() => {
    setShowTutorial(true);
  }, []);

  // Auto-run tour when user hasn't completed creator tutorial
  useEffect(() => {
    if (userInfo?.has_completed_creator_tutorial === false) {
      setRunTour(true);
    }
  }, [userInfo?.has_completed_creator_tutorial]);

  const enableSteps = (enabled: boolean = true) => {
    setRunTour(enabled);
  };

  const handleJoyrideCallback = (data: CallBackProps) => {
    const { status, type } = data;

    if ([STATUS.FINISHED, STATUS.SKIPPED].includes(status as any)) {
      setRunTour(false);

      // Call the completion handler when tour is finished
      if (status === STATUS.FINISHED && onCompleteTutorial) {
        onCompleteTutorial();
      }
    }
  };

  return {
    enableSteps,
    runTour,
    handleJoyrideCallback,
  };
};

function DesignStepContainer(props: Props) {
  const { userInfo, setUserInfo } = useUserInfoProvider();
  const { courseDetail } = props;
  const { completeCreatorTutorial } = useCompleteCreatorTutorial();

  const handleCompleteCreatorTutorial = async () => {
    if (userInfo?.info?.id) {
      const res = await completeCreatorTutorial({ id: userInfo.info.id });
      if (res?.status === HttpStatusCode.SUCCESS) {
        setUserInfo({
          token: userInfo.token,
          info: {
            ...userInfo.info,
            has_completed_creator_tutorial: true,
          },
        });
      }
    }
  };

  const { runTour, handleJoyrideCallback } = useTutorial(userInfo?.info, handleCompleteCreatorTutorial);

  const router = useRouter();
  const params = useParams<{ courseId: string }>();
  const { parsedQueryParams } = useSafeSearchParams<{ lessonId: string; sectionId: string; testId: string }>();
  const { lessonId, sectionId, testId } = parsedQueryParams;

  const courseDetailData = useCourseDetail({ courseDetail });
  const { sections, setSections } = useChapters({ courseDetail: courseDetailData! });

  usePreventHydration();

  const handleNextStep = () => {
    router.push(routePaths.course.publish.replace(':courseId', params.courseId));
  };

  const handleBackStep = () => {
    const url = formatApiUrl(routePaths.course.editInfo, { courseId: params.courseId });
    router.push(url);
  };

  const renderContent = () => {
    const shouldShowQuizDetail = sectionId && lessonId && testId;
    if (shouldShowQuizDetail) {
      return <QuizContainer key={testId} />;
    }

    const shouldShowFinalTestDetail = sectionId && testId;
    if (shouldShowFinalTestDetail) {
      return <FinalTestContainer key={testId} />;
    }

    const shouldShowLessonDetail = sectionId && lessonId;
    if (shouldShowLessonDetail) {
      return <LessonContainer />;
    }

    return (
      <ChaptersContainer
        key={courseDetailData?.id}
        courseDetail={courseDetailData!}
        sections={sections}
        setSections={setSections}
      />
    );
  };

  return (
    <DesignStepProvider value={{ courseDetail: courseDetailData! }}>
      <div className="flex h-screen flex-col overflow-hidden">
        <CreateCourseHeader
          stepIndex={1}
          onNext={handleNextStep}
          onBack={handleBackStep}
          slotProps={{
            setupItem: { status: 'finish', onClick: handleBackStep },
            designItem: { status: 'process', onClick: () => {} },
            publishItem: { status: 'wait', onClick: handleNextStep },
          }}
        />

        <main className="flex h-[calc(100vh-72px)] border-t border-neutral-200">
          <div className="h-full">
            <Sidebar sections={sections || []} />
          </div>

          <div className="size-full bg-neutral-50">{renderContent()}</div>
        </main>
      </div>

      <Joyride
        steps={steps}
        run={runTour}
        continuous={true}
        callback={handleJoyrideCallback}
        hideCloseButton={true}
        disableScrolling={true}
        disableOverlayClose={true}
        disableOverlay={false}
        showSkipButton={false}
        styles={{
          options: {
            zIndex: 10000,
            backgroundColor: '#0D0D59',
            arrowColor: '#0D0D59',
          },
          tooltip: {
            borderRadius: 8,
          },
          tooltipContainer: {
            textAlign: 'left' as const,
          },
          buttonNext: {
            backgroundColor: '#FFFFFF',
            fontSize: 12,
            width: '74px',
            height: '32px',
            color: '#2E2EE5',
          },
          buttonBack: {
            backgroundColor: '#0D0D59',
            fontSize: 12,
            width: '74px',
            height: '32px',
            color: '#FFFFFF',
            border: '1px solid #FFFFFF',
            borderRadius: '8px',
            position: 'absolute',
            left: '16px',
          },
          buttonClose: {
            color: '#FFFFFF',
            fontSize: 11,
          },
          tooltipTitle: {
            color: '#FFFFFF',
            fontSize: 11,
          },
          tooltipContent: {
            justifyContent: 'space-between',
          },
        }}
        locale={{
          back: 'Trở lại',
          close: 'Đóng',
          last: 'Đã hiểu',
          next: 'Tiếp tục',
        }}
      />
    </DesignStepProvider>
  );
}

export default DesignStepContainer;
