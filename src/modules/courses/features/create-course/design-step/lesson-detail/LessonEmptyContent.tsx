'use client';

import { Icon } from '@/components/client';
import { Button, Modal, Typography } from '@/components/ui';
import { cn } from '@/lib/utils';
import { LibraryModal } from '@/modules/courses/components';
import { InteractionType } from '@/modules/courses/constants/course.const';
import { LibraryFileType } from '@/modules/courses/constants/file.const';
import { useDesignStepProvider } from '@/modules/courses/features/create-course/design-step/DesignStepProvider';
import { useCourseEditable } from '@/modules/courses/hooks';
import { useLessonStore } from '@/z-store/lesson.store';
import { ArrowUpTrayIcon } from '@heroicons/react/24/outline';
import { Spin } from 'antd';
import Image from 'next/image';
import React from 'react';
import { useUpdateVideoFile } from '../hooks/useUpdateVideoFile';

export default function LessonEmptyContent() {
  const { onUpdateVideoFile: onAddVideo, isLoading: isAddingVideoToCourse } = useUpdateVideoFile();

  const { courseDetail } = useDesignStepProvider();

  const { canEditCourse, showCannotEditModal, contextHolderModal } = useCourseEditable();

  const { updateSettings } = useLessonStore();

  const [openLibraryModal, setOpenLibraryModal] = React.useState(false);

  const handleOpenLibraryModal = () => {
    if (!canEditCourse(courseDetail)) {
      showCannotEditModal();
      return;
    }

    setOpenLibraryModal(true);
  };

  React.useEffect(() => {
    updateSettings({ isOpenInteractionType: InteractionType.Default });
  }, []);

  return (
    <React.Fragment>
      <div className="flex size-full items-center justify-center">
        <div className="flex w-1/2 flex-col items-center justify-center gap-4">
          <Image src="/upload/light-bg-video.svg" alt="video empty data image" width={128} height={128} />

          <div className="flex flex-col items-center gap-2">
            <Typography variant="titleLg" className="text-center">
              Chưa có nội dung bài giảng video
            </Typography>

            <Typography variant="bodyLg" className="text-center">
              Tải lên video bài giảng để bắt đầu xây dựng nội dung khóa học và thêm các tương tác hỗ trợ quá trình học
              tập.
            </Typography>
          </div>

          <div>
            <Button onClick={handleOpenLibraryModal}>
              <div className="flex gap-2">
                <Icon icon={<ArrowUpTrayIcon />} />
                Thêm video
              </div>
            </Button>
          </div>
        </div>
      </div>

      {isAddingVideoToCourse && (
        <Modal open={isAddingVideoToCourse} width="184px" footer={null}>
          <div
            className={cn(
              'flex flex-col items-center justify-center gap-2 rounded-lg bg-base-white-50 p-6',
              '[&_.ant-spin-dot-circle:not(.ant-spin-dot-circle-bg)]:!stroke-black',
            )}
          >
            <Spin percent="auto" spinning={true} />
            <Typography variant="labelMd" className="text-center">
              Đang thêm video vào bài giảng
            </Typography>
          </div>
        </Modal>
      )}

      {openLibraryModal && (
        <LibraryModal
          title="Chọn video"
          open={openLibraryModal}
          enabledTypes={[LibraryFileType.VIDEO]}
          onAddFile={(file) => onAddVideo(file.id)}
          onClose={() => setOpenLibraryModal(false)}
        />
      )}

      {contextHolderModal}
    </React.Fragment>
  );
}
