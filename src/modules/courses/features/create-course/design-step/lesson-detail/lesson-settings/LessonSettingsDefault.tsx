import { Icon } from '@/components/client';
import { Button, Typography } from '@/components/ui';
import { cn } from '@/lib/utils';
import { LibraryModal } from '@/modules/courses/components';
import { LibraryFileType } from '@/modules/courses/constants/file.const';
import { useDesignStepProvider } from '@/modules/courses/features/create-course/design-step/DesignStepProvider';
import useLessonDetailActions from '@/modules/courses/features/create-course/design-step/hooks/useLessonDetailActions';
import { useUpdateVideoFile } from '@/modules/courses/features/create-course/design-step/hooks/useUpdateVideoFile';
import DeleteVideoModal from '@/modules/courses/features/create-course/design-step/lesson-detail/lesson-settings/DeleteVideoModal';
import ReplaceVideoModal from '@/modules/courses/features/create-course/design-step/lesson-detail/lesson-settings/ReplaceVideoModal';
import { useLessonProvider } from '@/modules/courses/features/create-course/design-step/lesson-detail/LessonProvider';
import { useCourseEditable } from '@/modules/courses/hooks';
import { LibraryFile } from '@/modules/courses/types/file.type';
import { ArrowPathRoundedSquareIcon, TrashIcon, VideoCameraIcon } from '@heroicons/react/24/outline';
import { Checkbox, Spin } from 'antd';
import Image from 'next/image';
import React from 'react';
import { match } from 'ts-pattern';

function VideoUploadedItem({
  videoName,
  onDelete,
  onReplace,
}: {
  videoName: string;
  onDelete: () => void;
  onReplace: () => void;
}) {
  const handleDelete = (e: React.MouseEvent<HTMLDivElement>) => {
    e.stopPropagation();
    onDelete();
  };

  return (
    <div
      className={cn(
        'flex w-full items-center justify-between rounded-lg border border-neutral-200 p-2',
        'cursor-pointer hover:bg-neutral-100',
        'translate-all transition duration-300',
      )}
      onClick={onReplace}
    >
      <div className="flex items-center gap-2">
        <Icon icon={<VideoCameraIcon className="size-6" />} />
        <Typography variant="labelMd" className="text-blue-500">
          {videoName}
        </Typography>
      </div>

      <div className="flex items-center gap-4">
        <Icon icon={<ArrowPathRoundedSquareIcon />} className="cursor-pointer" />
        <Icon icon={<TrashIcon className="text-red-500" />} className="cursor-pointer" onClick={handleDelete} />
      </div>
    </div>
  );
}

enum ModalType {
  Replace = 'REPLACE',
  Delete = 'DELETE',
  LibraryImage = 'LIBRARY_IMAGE',
  LibraryVideo = 'LIBRARY_VIDEO',
}

export default function LessonSettingsDefault() {
  const { lessonDetail } = useLessonProvider();

  const { isLoading: isDeleting, onUpdateVideoFile } = useUpdateVideoFile();
  const { onUpdateLessonDetail, onSetFreeContent, isSettingFreeContent, isLoading } = useLessonDetailActions();

  const { canEditCourse, showCannotEditModal, contextHolderModal } = useCourseEditable();
  const { courseDetail } = useDesignStepProvider();

  const [openModal, setOpenModal] = React.useState<ModalType | null>(null);

  const openReplaceVideoModal = openModal === ModalType.Replace;
  const openDeleteVideoModal = openModal === ModalType.Delete;

  const openLibraryModal = openModal === ModalType.LibraryImage || openModal === ModalType.LibraryVideo;

  const video = lessonDetail?.videoId;
  const hasVideo = !!video?.fileUrl;

  const handleOpenModal = (type: ModalType) => {
    if (!canEditCourse(courseDetail)) {
      showCannotEditModal();
      return;
    }

    setOpenModal(type);
  };

  const handleCloseModal = () => {
    setOpenModal(null);
  };

  const handleConfirmAddFile = (file: LibraryFile) => {
    return match(openModal)
      .with(ModalType.LibraryImage, () => onUpdateLessonDetail({ thumbnailFileId: file.id }))
      .with(ModalType.LibraryVideo, () => onUpdateVideoFile(file.id))
      .otherwise(() => {});
  };

  const handleDeleteLessonThumbnail = () => {
    if (!canEditCourse(courseDetail)) {
      showCannotEditModal();
      return;
    }

    onUpdateLessonDetail({ thumbnailFileId: null });
  };

  return (
    <Spin spinning={isSettingFreeContent || isLoading}>
      <React.Fragment>
        <div className="flex flex-col gap-2 p-4">
          <div className="mb-4 flex items-center justify-between gap-2">
            <Typography variant="labelMd">Cho phép học MIỄN PHÍ</Typography>
            <Checkbox
              checked={lessonDetail?.isFreeContent}
              onChange={(e) => onSetFreeContent(e.target.checked)}
              disabled={isSettingFreeContent}
            />
          </div>

          <Typography variant="labelMd">Ảnh đại diện bài học</Typography>

          <div className="flex h-44 w-full rounded-lg border border-neutral-200 p-2">
            <Image
              width={100}
              height={100}
              src={lessonDetail?.lectureThumbnailImage || '/images/placeholder.png'}
              alt="Upload placeholder"
              className="size-full rounded-md object-cover"
            />
          </div>

          <Typography variant="bodySm">
            Hãy sử dụng tệp hình ảnh có có đuôi .jpg hoặc .png với kích thước khuyến nghị 1280x720 (Tỷ lệ ảnh 16:9)
          </Typography>

          <div className="flex items-center gap-2">
            <Button
              variant="tertiary"
              className="max-w-fit"
              size="small"
              onClick={() => handleOpenModal(ModalType.LibraryImage)}
            >
              {lessonDetail?.lectureThumbnailImage ? 'Chọn ảnh khác' : 'Chọn ảnh'}
            </Button>

            {lessonDetail?.lectureThumbnailImage && (
              <Button
                variant="ghost-reversed"
                className="max-w-fit text-red-500 hover:bg-red-50"
                size="small"
                onClick={handleDeleteLessonThumbnail}
              >
                Xóa ảnh
              </Button>
            )}
          </div>
        </div>

        <div className="flex flex-col gap-2 p-4">
          <div className="flex gap-0.5">
            <Typography variant="labelMd">Nội dung bài học</Typography>
            <div className="text-red-600">*</div>
          </div>

          {hasVideo ? (
            <VideoUploadedItem
              videoName={lessonDetail?.videoId?.fileName || ''}
              onReplace={() => handleOpenModal(ModalType.Replace)}
              onDelete={() => handleOpenModal(ModalType.Delete)}
            />
          ) : (
            <Button
              variant="tertiary"
              className="max-w-fit"
              size="small"
              onClick={() => handleOpenModal(ModalType.LibraryVideo)}
            >
              Chọn video
            </Button>
          )}
        </div>

        {openReplaceVideoModal && (
          <ReplaceVideoModal
            open={openReplaceVideoModal}
            onConfirm={() => {
              if (video) {
                setOpenModal(ModalType.LibraryVideo);
              }
            }}
            onClose={() => setOpenModal(null)}
            onCancel={() => setOpenModal(null)}
          />
        )}

        {openDeleteVideoModal && (
          <DeleteVideoModal
            open={openDeleteVideoModal}
            isLoading={isDeleting}
            onConfirm={() => {
              onUpdateVideoFile('');
              setOpenModal(null);
            }}
            onClose={() => setOpenModal(null)}
            onCancel={() => setOpenModal(null)}
          />
        )}

        {openLibraryModal && (
          <LibraryModal
            title={openModal === ModalType.LibraryVideo ? 'Chọn video' : 'Chọn ảnh'}
            open={openLibraryModal}
            enabledTypes={openModal === ModalType.LibraryVideo ? [LibraryFileType.VIDEO] : [LibraryFileType.IMAGE]}
            onAddFile={handleConfirmAddFile}
            onClose={handleCloseModal}
          />
        )}

        {contextHolderModal}
      </React.Fragment>
    </Spin>
  );
}
