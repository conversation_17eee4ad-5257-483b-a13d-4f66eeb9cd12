import { Typography } from '@/components/ui';
import { cn } from '@/lib/utils';
import { InputEditor } from '@/modules/courses/components';
import { useDesignStepProvider } from '@/modules/courses/features/create-course/design-step/DesignStepProvider';
import useLessonDetailActions from '@/modules/courses/features/create-course/design-step/hooks/useLessonDetailActions';
import { useLessonProvider } from '@/modules/courses/features/create-course/design-step/lesson-detail/LessonProvider';
import { useCourseEditable } from '@/modules/courses/hooks';
import React from 'react';

type Props = {
  actions?: React.ReactNode;
};

export default function LessonTitle(props: Props) {
  const { actions } = props;

  const { lessonDetail } = useLessonProvider();

  const { onUpdateLessonDetail } = useLessonDetailActions();

  const { courseDetail } = useDesignStepProvider();

  const { contextHolderModal, canEditCourse, showCannotEditModal } = useCourseEditable();

  const [isEdit, setIsEdit] = React.useState(false);
  const [isHover, setIsHover] = React.useState(false);
  const [inputValue, setInputValue] = React.useState(lessonDetail?.lectureName || '');

  const sectionName = lessonDetail?.section?.sectionName || '';
  const lessonName = lessonDetail?.lectureName || '';

  const handleUpdateLessonTitle = () => {
    onUpdateLessonDetail({ lectureName: inputValue });
    setIsEdit(false);
  };

  const handleCancel = () => {
    setIsEdit(false);
    setInputValue(lessonName);
  };

  return (
    <div className="flex w-full justify-between">
      <div className="flex w-1/3 max-w-full flex-col gap-2">
        <Typography variant="labelMd">{sectionName}</Typography>

        <div className="relative">
          <div
            onMouseEnter={() => setIsHover(true)}
            onMouseLeave={() => setIsHover(false)}
            className={cn('-ml-2 w-full cursor-pointer px-2 py-1', isHover && 'rounded-lg bg-neutral-100 px-2 py-1')}
            onClick={() => {
              if (!canEditCourse(courseDetail)) {
                showCannotEditModal();
                return;
              }

              setIsEdit(true);
            }}
          >
            <Typography variant="headlineSm">{lessonName}</Typography>
          </div>

          {isEdit && (
            <div className="absolute left-0 top-0 z-50 w-full translate-y-10">
              <InputEditor
                value={inputValue}
                onEdit={handleUpdateLessonTitle}
                onChange={(e) => setInputValue(e.target.value)}
                onCancel={handleCancel}
              />
            </div>
          )}
        </div>
      </div>
      <div className="flex items-end">
        <div>{actions}</div>
      </div>

      {contextHolderModal}
    </div>
  );
}
