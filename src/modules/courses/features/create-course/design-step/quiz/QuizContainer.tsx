'use client';

import { useNotification, useSafeSearchParams } from '@/hooks';
import { useDesignStepProvider } from '@/modules/courses/features/create-course/design-step/DesignStepProvider';
import useTestActions from '@/modules/courses/features/create-course/design-step/hooks/useTestActions';
import { QuizFormData } from '@/modules/courses/features/create-course/design-step/quiz/quiz-form.type';
import { useCourseEditable } from '@/modules/courses/hooks';
import { zodResolver } from '@hookform/resolvers/zod';
import React from 'react';
import { FormProvider, useForm } from 'react-hook-form';
import QuestionForm from './QuestionForm';
import { getQuizDefaultValues, getQuizPayloadRequest, quizSchema } from './quiz.schema';
import QuizHeader from './QuizHeader';
import QuizSettings from './QuizSettings';

const useQuizForm = () => {
  const notification = useNotification();

  const { parsedQueryParams } = useSafeSearchParams<{ sectionId: string; testId: string; lessonId: string }>();
  const { onUpdateTest, testDetail } = useTestActions();

  const { courseDetail } = useDesignStepProvider();

  const { canEditCourse, showCannotEditModal, contextHolderModal } = useCourseEditable();

  const formMethods = useForm({ mode: 'all', resolver: zodResolver(quizSchema), defaultValues: {} });

  const handleInvalid = (errors: any) => {
    const qErr = errors?.questions;
    const qArr = Array.isArray(qErr) ? qErr : [];

    const hasEmptyRequired =
      qArr.some((q: any) => q?.questionName?.message) ||
      qArr.some(
        (q: any) => Array.isArray(q?.questionAnswers) && q.questionAnswers.some((a: any) => a?.answerName?.message),
      );

    const missingCorrect = qArr.some((q: any) => q?.questionCorrectAnswer?.message);

    if (hasEmptyRequired && missingCorrect) {
      return notification.error({
        message: 'Lưu bài kiểm tra không thành công',
        description: 'Vui lòng không để trống các trường bắt buộc và chọn đáp án đúng cho câu hỏi',
      });
    }

    if (hasEmptyRequired) {
      return notification.error({
        message: 'Lưu bài kiểm tra không thành công',
        description: 'Vui lòng không để trống các trường bắt buộc',
      });
    }

    if (missingCorrect) {
      return notification.error({
        message: 'Lưu bài kiểm tra không thành công',
        description: 'Vui lòng chọn đáp án đúng cho câu hỏi!',
      });
    }

    return notification.error({
      message: 'Lưu bài kiểm tra không thành công',
      description: 'Vui lòng kiểm tra lại thông tin bài kiểm tra',
    });
  };

  const handleSaveTest = (values: QuizFormData) => {
    if (!canEditCourse(courseDetail)) {
      showCannotEditModal();
      return;
    }

    if (!testDetail) return;

    const payload = getQuizPayloadRequest(values, testDetail, parsedQueryParams.lessonId);
    onUpdateTest(payload, {
      onSuccess: () => {
        notification.success({ message: 'Lưu bài ôn tập thành công' });
      },
      onError: () => {
        notification.error({ message: 'Lưu bài ôn tập thất bại' });
      },
    });
    formMethods.reset(values);
  };

  React.useEffect(() => {
    if (testDetail) {
      const defaultValues = getQuizDefaultValues(testDetail);
      formMethods.reset(defaultValues);
    }
  }, [testDetail]);

  return {
    formMethods,
    onInvalid: handleInvalid,
    onSaveTest: handleSaveTest,

    contextHolderModal,
  };
};

export default function QuizContainer() {
  const { formMethods, contextHolderModal, onInvalid, onSaveTest } = useQuizForm();

  return (
    <FormProvider {...formMethods}>
      <form onSubmit={formMethods.handleSubmit(onSaveTest, onInvalid)} className="h-full">
        <div className="flex h-full flex-col gap-4 overflow-y-auto px-8 py-10">
          <QuizHeader />

          <div className="flex h-full min-h-fit gap-6">
            <QuestionForm />
            <QuizSettings />
          </div>
        </div>
      </form>

      {contextHolderModal}
    </FormProvider>
  );
}
