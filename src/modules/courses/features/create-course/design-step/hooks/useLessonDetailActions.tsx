import { QUERY_KEYS } from '@/constants/query-keys';
import { useSafeSearchParams } from '@/hooks';
import { editLessonService, getLessonById, setFreeContentService } from '@/modules/courses/services/course.service';
import { LessonEditRequest } from '@/modules/courses/types/course-request.type';
import { useParams } from 'next/navigation';
import { useMutation, useQuery, useQueryClient } from 'react-query';

const useLessonDetailActions = () => {
  const queryClient = useQueryClient();

  const { parsedQueryParams } = useSafeSearchParams<{ sectionId: string; lessonId: string }>();
  const params = useParams<{ courseId: string }>();
  const courseId = params.courseId;

  const { sectionId, lessonId } = parsedQueryParams;

  const { mutate: updateLessonDetail, isLoading } = useMutation({
    mutationFn: editLessonService,
  });

  const { mutate: setFreeContent, isLoading: isSettingFreeContent } = useMutation({
    mutationFn: setFreeContentService,
  });

  const { data: lesson } = useQuery({
    queryKey: [QUERY_KEYS.LECTURE_DETAIL, courseId, sectionId, lessonId],
    queryFn: () => getLessonById({ courseId, sectionId, lectureId: lessonId }),
  });

  const handleUpdateLesson = ({
    lectureName,
    thumbnailFileId,
  }: Partial<Pick<LessonEditRequest, 'lectureName' | 'thumbnailFileId'>>) => {
    const variables = {
      courseId,
      sectionId: sectionId,
      lectureId: lessonId,
      lectureName,
      thumbnailFileId,
    };

    updateLessonDetail(variables, {
      onSuccess: () => {
        queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.LECTURE_DETAIL, courseId, sectionId, lessonId] });
      },
    });
  };

  const handleSetFreeContent = (isFreeContent: boolean) => {
    setFreeContent(
      { courseId, sectionId, lectureId: lessonId, isFreeContent },
      {
        onSuccess: () => {
          queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.LECTURE_DETAIL, courseId, sectionId, lessonId] });
        },
      },
    );
  };

  return {
    isLoading,
    isSettingFreeContent,
    lessonDetail: lesson,
    onUpdateLessonDetail: handleUpdateLesson,
    onSetFreeContent: handleSetFreeContent,
  };
};

export default useLessonDetailActions;
