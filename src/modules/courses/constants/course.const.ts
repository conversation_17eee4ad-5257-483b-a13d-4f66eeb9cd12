export enum ChapterType {
  Default = 'DEFAULT',
  Test = 'TEST',
}

export enum LessonType {
  Video = 'VIDEO',
  Test = 'TEST',
}

export enum InteractionType {
  Default = 'DEFAULT',
  QuickQuestion = 'QUESTION',
  Explore = 'EXPLORE',
}

export enum CourseStatusEnum {
  DRAFT = 'DRAFT',
  PENDING = 'PENDING',
  APPROVED = 'APPROVED',
  HIDDEN = 'HIDDEN',
  IN_REVIEW = 'IN_REVIEW',
  IN_REVISION = 'IN_REVISION',
}

export const SECTION_TYPE_MAP: Record<ChapterType, string> = {
  [ChapterType.Default]: 'Chương',
  [ChapterType.Test]: 'Bài kiểm tra',
};

export const LECTURE_TYPE_MAP: Record<LessonType, string> = {
  [LessonType.Video]: 'Bài giảng video',
  [LessonType.Test]: 'Bài kiểm tra',
};
