import { QUERY_KEYS } from '@/constants/query-keys';
import { updateFavoriteCreatorService } from '@/modules/courses/services/creator.service';
import { UserInfo } from '@/type';
import { notifyError, notifySuccess } from '@/utils';
import { useMutation, useQueryClient } from 'react-query';

const showSuccessNotification = (isFavorite: boolean) => {
  if (isFavorite) {
    notifySuccess('Thêm creator yêu thích thành công!');
  } else {
    notifySuccess('Xóa creator yêu thích thành công!');
  }
};

const showErrorNotification = (message: string) => {
  notifyError(message);
};

export const useFavoriteCreator = () => {
  const queryClient = useQueryClient();

  const { mutate: updateFavoriteCreatorMutate, isLoading: isUpdatingFavorite } = useMutation(
    (payload: { authorId: string; isFavorite: boolean }) => updateFavoriteCreatorService(payload),
  );

  const handleFavorite = ({
    authorId,
    isFavorite,
    onSuccess,
  }: {
    authorId: string;
    isFavorite: boolean;
    onSuccess?: () => void;
  }) => {
    updateFavoriteCreatorMutate(
      { authorId, isFavorite },
      {
        onSuccess: () => {
          showSuccessNotification(isFavorite);
          onSuccess?.();
        },
        onError: (err: unknown) => {
          const error = err as { data: { message: string } };
          const message = error?.data?.message;
          showErrorNotification(message);
        },
      },
    );
  };

  const handleUpdateQueryCreatorData = (onUpdateQuery: (oldData: UserInfo[] | undefined) => UserInfo[]) => {
    queryClient.setQueryData([QUERY_KEYS.FAVORITE_CREATORS], (oldData: UserInfo[] | undefined) => {
      const updated = onUpdateQuery(oldData);
      return updated ?? [];
    });
  };

  return {
    isUpdatingFavorite,
    onFavorite: handleFavorite,
    onUpdateQuery: handleUpdateQueryCreatorData,
  };
};
