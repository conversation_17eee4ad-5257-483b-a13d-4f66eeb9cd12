import { Typography } from '@/components/ui';
import { CourseStatusEnum } from '@/modules/courses/constants/course.const';
import { CourseInfo } from '@/modules/courses/types/course.type';
import { Modal } from 'antd';

const useCourseEditable = () => {
  const [modal, contextHolder] = Modal.useModal();

  const canEditCourse = (course: CourseInfo | null | undefined) => {
    const isEditable = course?.status === CourseStatusEnum.DRAFT || course?.status === CourseStatusEnum.IN_REVISION;
    return isEditable;
  };

  const showCannotEditModal = () => {
    modal.info({
      title: <Typography variant="headlineXs">Không thể chỉnh sửa khóa học</Typography>,
      content: (
        <div className="py-4">
          <Typography variant="bodyLg">Khóa học đang chờ duyệt.</Typography>
        </div>
      ),
      closable: true,
      footer: null,
      centered: true,
      width: 500,
    });
  };

  return { contextHolderModal: contextHolder, canEditCourse, showCannotEditModal };
};

export default useCourseEditable;
