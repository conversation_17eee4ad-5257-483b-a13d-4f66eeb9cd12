import { QUERY_KEYS } from '@/constants/query-keys';
import { getCourseByIdService } from '@/modules/courses/services/course.service';
import { CourseInfo } from '@/modules/courses/types/course.type';
import { useQuery } from 'react-query';

const useCourseDetail = ({
  courseId,
  initialData,
  queryFn,
}: {
  courseId: string;
  initialData?: CourseInfo;
  queryFn?: ({ courseId }: { courseId: string }) => Promise<CourseInfo | null>;
}) => {
  const { data } = useQuery({
    queryKey: [QUERY_KEYS.COURSE_DETAIL, courseId],
    enabled: !!courseId,
    initialData: initialData,
    queryFn: () => (queryFn ? queryFn({ courseId }) : getCourseByIdService(courseId)),
  });

  return {
    courseDetailData: data || null,
  };
};

export default useCourseDetail;
