export const formatApiUrl = (url: string, params: Record<string, string | number>): string => {
  let formattedUrl = url;
  for (const [key, value] of Object.entries(params)) {
    formattedUrl = formattedUrl.replace(`:${key}`, value.toString());
  }
  return formattedUrl;
};

/**
 * <PERSON>les redirect to billing cycle page with optional return URL
 * @param router - Next.js router instance
 * @param returnUrl - Optional URL to return to after billing cycle. If not provided, clears any stored redirectUrl
 */
export const redirectToBillingCycle = (router: { push: (url: string) => void }, returnUrl?: string): void => {
  if (typeof window !== 'undefined') {
    if (returnUrl) {
      localStorage.setItem('redirectUrl', returnUrl);
    } else {
      localStorage.removeItem('redirectUrl');
    }
  }
  router.push('/payment/billing-cycle');
};
