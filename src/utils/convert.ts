import { HyperNoteBody } from 'components/learner/type';
import { FontFamily } from 'constants/option';
import dayjs, { Dayjs } from 'dayjs';
import { CourseFile, DataLayout, FullCourseInfo, Lecture, Section, SlideItem, TextControl } from 'type';

export const convertDataAfterSend = (data: Record<string, any>) => {
  const cloneData = { ...data };
  Object.keys(cloneData).forEach((key: string) => {
    if (Array.isArray(data[key])) {
      cloneData[key] = data[key].join();
    }
  });
  return cloneData;
};

export const previewImage = (file: File, setImageUrl: (url: string) => void) => {
  const reader = new FileReader();
  reader.onload = () => {
    const imageUrl = reader.result as string;
    setImageUrl(imageUrl);
  };
  // Đọc file
  reader.readAsDataURL(file);
};

export const messageMaxLength = (maxLength: number) => {
  return `T<PERSON>i đa ${maxLength} ký tự`;
};

export const getSectionInCourse = (course: FullCourseInfo, sectionId: string | number) => {
  if (course) {
    const id = typeof sectionId === 'number' ? sectionId : parseInt(sectionId);
    return course.sections?.find((section) => section.id === id);
  }
};

export const getFileNameAudioById = (id: string | number | null | undefined, audios: CourseFile[]) => {
  return audios.find((audio: CourseFile) => audio.id === id)?.file_name;
};

export const getLectureInCourse = (courses: FullCourseInfo, sectionId: string, lectureId: string) => {
  if (courses && sectionId && lectureId) {
    const section: Section | undefined = getSectionInCourse(courses, sectionId);
    if (section) {
      return section.lectures.find((lecture) => lecture.id === parseInt(lectureId));
    }
  }
};

export const getBodyCreateNote = (params: {
  hyperValue: HyperNoteBody;
  selectedLecture: Lecture | null | undefined;
  courseInfo: FullCourseInfo;
  selectedSlide: SlideItem | null | undefined;
  sectionId: string | undefined;
  lectureId: string | undefined;
  currentTime: number | undefined;
  selectedText: DataLayout;
  currentSlideIdx: number;
}) => {
  const {
    hyperValue,
    selectedLecture,
    courseInfo,
    selectedSlide,
    sectionId,
    lectureId,
    selectedText,
    currentTime,
    currentSlideIdx,
  } = params;

  return {
    ...hyperValue,
    lecture_id: selectedLecture?.id,
    type_id: hyperValue.type_id ? hyperValue.type_id : 1,
    course_id: courseInfo.id!,
    extra_info: {
      course_name: courseInfo.course_name,
      section_name: getSectionInCourse(courseInfo, sectionId!)?.section_name ?? '',
      lecture_name: getLectureInCourse(courseInfo, sectionId!, lectureId!)?.lecture_name ?? '',
      lecture_type_id: selectedLecture?.lecture_type_id,
      slide: {
        slide_item_type_id: selectedSlide?.slide_item_type_id,
        slide_id: selectedSlide?.id,
        dataLayout: selectedText,
        currentSlideIdx,
      },
      current_time: currentTime ?? 0,
      totalTime: selectedLecture?.video?.file_duration ? Number(selectedLecture.video.file_duration) : 0,
    },
  };
};

export const getIndexSlideById = (slideId: number | null | undefined, slides: SlideItem[] | undefined) => {
  if (!slideId || !slides) return -1;
  return slides.findIndex((slideItem: SlideItem) => slideItem.id === slideId);
};

export const getSlideInLecture = (lecture: Lecture | null | undefined, currentSlideIdx: number) => {
  if (lecture && (currentSlideIdx !== null || true)) {
    const slide_items = lecture?.slide?.slide_items || [];
    const selectedSlide: SlideItem | null = slide_items[currentSlideIdx];
    return slide_items.find((slideItem: SlideItem) => slideItem.id === selectedSlide.id);
  }
  return null;
};

export const getIndexSectionInCourse = (courses: FullCourseInfo, sectionId: string | null | undefined) => {
  if (!courses || !sectionId) return 0;
  return courses.sections.findIndex((section) => section.id === parseInt(sectionId));
};

type FormatTime = 'HHMMSS' | 'MMSS' | 'SS';
export const secondToHHMMSS = (seconds: number, format: FormatTime = 'HHMMSS') => {
  if (isNaN(seconds)) {
    return `00:00:00`;
  }
  const date = new Date(seconds * 1000);
  const hh = date.getUTCHours();
  const mm = date.getUTCMinutes().toString().padStart(2, '0');
  const ss = date.getUTCSeconds().toString().padStart(2, '0');
  if (format === 'SS') {
    return ss;
  } else if (format === 'MMSS') {
    return `${mm}:${ss}`;
  } else {
    return `${hh}:${mm}:${ss}`;
  }
};

export const convertHHMMSSToSeconds = (time = '00:00:00'): number => {
  const [hh, mm, ss]: any = time.split(':');
  return parseInt(hh) * 3600 + parseInt(mm) * 60 + parseInt(ss);
};

export const getSecondFromMMSS = (time: string): number => {
  const [mm, ss] = time.split(':');
  let second = 0;
  if (mm) {
    second += +mm * 60;
  }
  if (ss) {
    second += +ss;
  }
  return second;
};
export const convertDayjsToHHMMSS = (start: Dayjs, end: Dayjs) => {
  const diffMilliseconds = dayjs(end, 'HH:mm:ss').diff(dayjs(start, 'HH:mm:ss'));
  const diffSeconds = diffMilliseconds / 1000;
  const hours = Math.floor(diffSeconds / 3600);
  const minutes = Math.floor((diffSeconds % 3600) / 60);
  const seconds = Math.floor(diffSeconds % 60);
  return `${hours}:${minutes}:${seconds}`;
};

export const convertPercentToSecond = (percent: number, time: number): number => {
  return +((percent * time) / 100)?.toFixed(2);
};

export const convertSecondToPercent = (second: number, time: number): number => {
  return +((second * 100) / time)?.toFixed(2);
};

export const secondToDate = (second: number, format = 'HH:mm:ss'): dayjs.Dayjs => {
  return dayjs(secondToHHMMSS(second), format);
};

export const getFontFamily = (font: string) => {
  switch (font) {
    case FontFamily.Be:
      return "'Be Vietnam Pro', sans-serif";
    case FontFamily.Poppins:
      return "'SVN-Poppins', sans-serif";
    case FontFamily.Inter:
      return "'Inter', sans-serif";
    case FontFamily.Roboto:
      return "'Roboto', sans-serif";
    case FontFamily.Notosans:
      return "'Noto Sans', sans-serif";
    case FontFamily.Plus_Jakarta_Sans:
      return "'Plus Jakarta Sans', sans-serif";
    case FontFamily.Roboto_slab:
      return "'Roboto Slab', serif";
    case FontFamily.Noto_Serif:
      return "'Noto Serif', serif";
    case FontFamily.Playfair:
      return "'Playfair Display', serif";
  }
};

export const getStyleAction = (action: number) => {
  switch (action) {
    case 1:
      return 'underline';
    case 2:
      return 'line-through';
    case 3:
      return 'top';
    case 4:
      return 'middle';
    case 5:
      return 'bottom';
    default:
      return 'top';
  }
};

export const getStyle: any = (textControl: TextControl) => {
  const { font, background } = textControl ?? {};
  const { textAlign, fontFamily, fontWeight, action, color, fontSize } = font ?? {};
  const { backgroundColor, opacity } = background ?? {};

  return {
    textAlign: textAlign || 'left',
    fontFamily: getFontFamily(fontFamily),
    color,
    fontSize,
    [`${fontWeight !== 'Italic' ? 'fontWeight' : 'fontStyle'}`]: fontWeight,
    [`${action === 1 || action === 2 ? 'textDecoration' : 'verticalAlign'}`]: getStyleAction(action),
    backgroundColor: convertHexToRgba(backgroundColor, opacity / 100),
  };
};

export function convertHexToRgba(hex: string, opacity: number) {
  if (!hex) return '#000000';

  hex = hex.replace('#', '');
  let r = parseInt(hex.substring(0, 2), 16);
  let g = parseInt(hex.substring(2, 4), 16);
  let b = parseInt(hex.substring(4, 6), 16);
  return 'rgba(' + r + ', ' + g + ', ' + b + ', ' + opacity + ')';
}
