import crypto from 'crypto';

/**
 * Creates a hash from user_id and plan_id for the request_key
 * @param userId - The user ID
 * @param planId - The plan ID
 * @returns Hashed string for request_key
 */
export const createRequestKey = (userId: string, planId: string): string => {
  const combined = `${userId}+${planId}`;

  // Create hash using crypto (browser compatible)
  if (typeof window !== 'undefined') {
    // Browser environment - use Web Crypto API or simple hash
    return btoa(combined + Date.now().toString())
      .replace(/[^a-zA-Z0-9]/g, '')
      .substring(0, 16);
  } else {
    // Node environment
    return crypto.createHash('md5').update(combined).digest('hex').substring(0, 16);
  }
};

/**
 * Generates a unique request key with timestamp for uniqueness
 * @param userId - The user ID
 * @param planId - The plan ID
 * @returns Unique request key
 */
export const generateUniqueRequestKey = (userId: string, planId: string): string => {
  const timestamp = Date.now().toString();
  const combined = `${userId}-${planId}-${timestamp}`;

  if (typeof window !== 'undefined') {
    // Simple hash for browser
    // return btoa(combined).replace(/[^a-zA-Z0-9]/g, '').substring(0, 20);
    return new Date().getTime().toString();
  } else {
    return crypto.createHash('sha256').update(combined).digest('hex').substring(0, 20);
  }
};
