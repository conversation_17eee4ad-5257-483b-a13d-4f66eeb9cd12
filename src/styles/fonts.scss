/* SVN Poppins Font Family */

@font-face {
  font-family: 'SVN-Poppins';
  src: url('/fonts/svn-poppins/SVN-Poppins-Thin.otf') format('opentype');
  font-weight: 100;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'SVN-Poppins';
  src: url('/fonts/svn-poppins/SVN-Poppins-ThinItalic.otf') format('opentype');
  font-weight: 100;
  font-style: italic;
  font-display: swap;
}

@font-face {
  font-family: 'SVN-Poppins';
  src: url('/fonts/svn-poppins/SVN-Poppins-ExtraLight.otf') format('opentype');
  font-weight: 200;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'SVN-Poppins';
  src: url('/fonts/svn-poppins/SVN-Poppins-ExtraLightItalic.otf') format('opentype');
  font-weight: 200;
  font-style: italic;
  font-display: swap;
}

@font-face {
  font-family: 'SVN-Poppins';
  src: url('/fonts/svn-poppins/SVN-Poppins-Light.otf') format('opentype');
  font-weight: 300;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'SVN-Poppins';
  src: url('/fonts/svn-poppins/SVN-Poppins-LightItalic.otf') format('opentype');
  font-weight: 300;
  font-style: italic;
  font-display: swap;
}

@font-face {
  font-family: 'SVN-Poppins';
  src: url('/fonts/svn-poppins/SVN-Poppins-Regular.otf') format('opentype');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'SVN-Poppins';
  src: url('/fonts/svn-poppins/SVN-Poppins-Italic.otf') format('opentype');
  font-weight: 400;
  font-style: italic;
  font-display: swap;
}

@font-face {
  font-family: 'SVN-Poppins';
  src: url('/fonts/svn-poppins/SVN-Poppins-Medium.otf') format('opentype');
  font-weight: 500;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'SVN-Poppins';
  src: url('/fonts/svn-poppins/SVN-Poppins-MediumItalic.otf') format('opentype');
  font-weight: 500;
  font-style: italic;
  font-display: swap;
}

@font-face {
  font-family: 'SVN-Poppins';
  src: url('/fonts/svn-poppins/SVN-Poppins-SemiBold.otf') format('opentype');
  font-weight: 600;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'SVN-Poppins';
  src: url('/fonts/svn-poppins/SVN-Poppins-SemiBoldItalic.otf') format('opentype');
  font-weight: 600;
  font-style: italic;
  font-display: swap;
}

@font-face {
  font-family: 'SVN-Poppins';
  src: url('/fonts/svn-poppins/SVN-Poppins-Bold.otf') format('opentype');
  font-weight: 700;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'SVN-Poppins';
  src: url('/fonts/svn-poppins/SVN-Poppins-BoldItalic.otf') format('opentype');
  font-weight: 700;
  font-style: italic;
  font-display: swap;
}

@font-face {
  font-family: 'SVN-Poppins';
  src: url('/fonts/svn-poppins/SVN-Poppins-ExtraBold.otf') format('opentype');
  font-weight: 800;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'SVN-Poppins';
  src: url('/fonts/svn-poppins/SVN-Poppins-ExtraBoldItalic.otf') format('opentype');
  font-weight: 800;
  font-style: italic;
  font-display: swap;
}

@font-face {
  font-family: 'SVN-Poppins';
  src: url('/fonts/svn-poppins/SVN-Poppins-Black.otf') format('opentype');
  font-weight: 900;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'SVN-Poppins';
  src: url('/fonts/svn-poppins/SVN-Poppins-BlackItalic.otf') format('opentype');
  font-weight: 900;
  font-style: italic;
  font-display: swap;
}
