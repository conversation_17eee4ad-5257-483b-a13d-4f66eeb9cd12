'use client';
import { Button } from 'antd';
import Image from 'next/image';
import React from 'react';
import { ONEInteractive } from './ONEInteractive';

interface ONEProps {
  className?: string;
}

const oneMedia = [
  {
    id: 'one-1',
    src: '/images/creator-one-1.png',
    alt: '<PERSON><PERSON><PERSON> đồng ONE - Kết nối',
    width: 672,
    height: 448,
    type: 'image' as const,
  },
  {
    id: 'one-2',
    src: '/images/creator-one-2.png',
    alt: 'Cộng đồng ONE - Chia sẻ',
    width: 321,
    height: 344,
    type: 'image' as const,
  },
  {
    id: 'one-3',
    src: '/images/creator-one-3.png',
    alt: '<PERSON><PERSON><PERSON> đồng ONE - <PERSON>áng tạo',
    width: 251,
    height: 448,
    type: 'image' as const,
  },
  {
    id: 'one-4',
    src: '/images/creator-one-4.png',
    alt: '<PERSON><PERSON><PERSON> đồng ONE - <PERSON><PERSON><PERSON> đồng',
    width: 517,
    height: 344,
    type: 'image' as const,
  },
  {
    id: 'one-5',
    src: '/images/creator-one-5.png',
    alt: '<PERSON><PERSON><PERSON> đồng ONE - <PERSON>ọc tập',
    width: 321,
    height: 488,
    type: 'image' as const,
  },
  {
    id: 'one-6',
    src: '/videos/clip-bec-1.mp4',
    alt: 'Cộng đồng ONE - Tương tác',
    width: 796,
    height: 448,
    type: 'video' as const,
  },
  {
    id: 'one-7',
    src: '/images/creator-one-7.png',
    alt: 'Cộng đồng ONE - Phát triển',
    width: 516,
    height: 344,
    type: 'image' as const,
  },
  {
    id: 'one-8',
    src: '/images/creator-one-8.png',
    alt: 'Cộng đồng ONE - Chia sẻ kinh nghiệm',
    width: 320,
    height: 396,
    type: 'image' as const,
  },
  {
    id: 'one-9',
    src: '/images/creator-one-9.png',
    alt: 'Cộng đồng ONE - Hỗ trợ',
    width: 594,
    height: 396,
    type: 'image' as const,
  },
  {
    id: 'one-10',
    src: '/images/creator-one-10.png',
    alt: 'Cộng đồng ONE - Kết nối mạng',
    width: 336,
    height: 448,
    type: 'image' as const,
  },
  {
    id: 'one-11',
    src: '/videos/clip-bec-2.mp4',
    alt: 'Cộng đồng ONE - Cộng đồng lớn',
    width: 612,
    height: 344,
    type: 'video' as const,
  },
];

export const ONE: React.FC<ONEProps> = ({ className = '' }) => {
  return (
    <section className={`bg-white py-16 sm:py-20 lg:py-24 ${className}`}>
      <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
        <div className="mb-8 md:mb-10">
          <h2 className="mb-6 text-headline-lg font-semibold text-primary_text md:text-display-md md:font-bold">
            Online Network of Edu-Creators -{' '}
            <span className="inline-block rounded-lg bg-secondary-300 px-3 py-1 font-extrabold text-primary-500">
              ONE
            </span>
          </h2>
          <div className="max-w-4xl">
            <p className="text-label-lg font-medium leading-relaxed text-[#21272A] md:text-headline-xs md:font-semibold">
              Là mạng lưới các Edu-Creators hàng đầu tại Studify - nơi bạn sẽ được kết nối, hợp tác và phát triển thông
              qua các dự án và chương trình độc quyền. Tham gia ONE ngay hôm nay để khẳng định dấu ấn riêng của bạn trên
              hành trình kiến tạo tri thức.
            </p>
          </div>
        </div>

        <div className="mb-12 flex flex-col gap-4 sm:mb-16 sm:flex-row sm:gap-6">
          <Button
            type="primary"
            className="rounded-xl px-8 py-3 font-medium"
            onClick={() => {
              window.open('mailto:<EMAIL>');
            }}
          >
            Liên hệ tư vấn
          </Button>
          <Button
            type="default"
            className="rounded-xl border border-primary-500 px-8 py-3 font-medium text-primary-500"
          >
            Tìm hiểu thêm về ONE
          </Button>
        </div>

        <div className="relative overflow-hidden">
          <div id="one-slider" className="flex gap-8" style={{ transform: 'translateX(0px)' }}>
            {[...oneMedia, ...oneMedia].map((media, index) => (
              <div key={`${media.id}-${index}`} className="shrink-0">
                <div className="overflow-hidden rounded-3xl">
                  {media.type === 'image' ? (
                    <Image
                      src={media.src}
                      alt={media.alt}
                      width={media.width}
                      height={media.height}
                      className="h-auto object-contain"
                      quality={100}
                      sizes={`(max-width: 768px) 100vw, (max-width: 1200px) ${media.width}px, ${media.width}px`}
                    />
                  ) : (
                    <video
                      width={media.width}
                      height={media.height}
                      className="h-auto object-contain"
                      autoPlay
                      muted
                      loop
                      playsInline
                      preload="metadata"
                    >
                      <source src={media.src} type="video/mp4" />
                      Your browser does not support the video tag.
                    </video>
                  )}
                </div>
              </div>
            ))}
          </div>
        </div>

        <ONEInteractive images={oneMedia} />
      </div>
    </section>
  );
};

export default ONE;
