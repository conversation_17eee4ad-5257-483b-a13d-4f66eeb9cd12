'use client';

import { useEffect } from 'react';

interface FAQItem {
  id: string;
  question: string;
  answer: string;
}

interface FAQInteractiveProps {
  faqItems: FAQItem[];
}

export const FAQInteractive: React.FC<FAQInteractiveProps> = ({ faqItems }) => {
  useEffect(() => {
    const toggleFAQ = (itemId: string) => {
      const content = document.getElementById(`faq-content-${itemId}`);
      const button = document.getElementById(`faq-button-${itemId}`);
      const icon = document.getElementById(`faq-icon-${itemId}`);

      if (content && button && icon) {
        const isExpanded = button.getAttribute('aria-expanded') === 'true';

        if (isExpanded) {
          content.style.maxHeight = '0px';
          content.setAttribute('aria-hidden', 'true');
          button.setAttribute('aria-expanded', 'false');

          icon.style.transform = 'rotate(0deg)';

          icon.innerHTML = `
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M12 6v6m0 0v6m0-6h6m-6 0H6"
            />
          `;
        } else {
          content.style.maxHeight = 'none';
          const height = content.scrollHeight;
          content.style.maxHeight = '0px';

          requestAnimationFrame(() => {
            content.style.maxHeight = `${height}px`;
            content.setAttribute('aria-hidden', 'false');
            button.setAttribute('aria-expanded', 'true');

            icon.style.transform = 'rotate(45deg)';
          });
        }
      }
    };

    const eventHandlers = new Map();

    faqItems.forEach((item) => {
      const button = document.getElementById(`faq-button-${item.id}`);
      if (button) {
        const clickHandler = () => toggleFAQ(item.id);
        const keydownHandler = (event: KeyboardEvent) => {
          if (event.key === 'Enter' || event.key === ' ') {
            event.preventDefault();
            toggleFAQ(item.id);
          }
        };

        button.addEventListener('click', clickHandler);
        button.addEventListener('keydown', keydownHandler);

        eventHandlers.set(item.id, { clickHandler, keydownHandler });
      }
    });

    return () => {
      faqItems.forEach((item) => {
        const button = document.getElementById(`faq-button-${item.id}`);
        const handlers = eventHandlers.get(item.id);

        if (button && handlers) {
          button.removeEventListener('click', handlers.clickHandler);
          button.removeEventListener('keydown', handlers.keydownHandler);
        }
      });
    };
  }, [faqItems]);

  return null;
};
