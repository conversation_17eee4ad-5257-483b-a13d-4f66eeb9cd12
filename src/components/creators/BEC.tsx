'use client';
import Image from 'next/image';
import React from 'react';
import { BECInteractive } from './BECInteractive';

interface BECProps {
  className?: string;
}

interface TabContent {
  id: string;
  title: string;
  content: string;
  image: string;
  background: string;
  icon: string;
  activeIcon: string;
  sticker1: string;
  sticker2: string;
  foreground: string;
  sticker1Size: { desktop: { width: number; height: number }; mobile: { width: number; height: number } };
  sticker2Size: { desktop: { width: number; height: number }; mobile: { width: number; height: number } };
}

const tabData: TabContent[] = [
  {
    id: 'create',
    title: 'Chiến thần Sáng tạo',
    content:
      'Bạn là **nhà sáng tạo nội dung giáo dục** hoặc cá nhân đam mê chia sẻ kiến thức đang tìm kiếm bộ công cụ sáng tạo mạnh mẽ giúp phát triển các khóa học độc đáo, biến ý tưởng thành nội dung giáo dục số thu hút.',
    image: '/images/BEC-talent-1.png',
    background: '/images/Rec-1.png',
    icon: '/images/bec-icon-1.svg',
    activeIcon: '/images/bec-icon-1a.svg',
    sticker1: '/images/bec-sticker-1.svg',
    sticker2: '/images/bec-sticker-2.svg',
    foreground: '/images/bec-foreground-1.png',
    sticker1Size: { desktop: { width: 80, height: 80 }, mobile: { width: 80, height: 80 } },
    sticker2Size: { desktop: { width: 121, height: 60 }, mobile: { width: 90, height: 45 } },
  },
  {
    id: 'expertise',
    title: 'Chuyên gia Năng động',
    content:
      'Bạn là **chuyên gia, nhà đào tạo** từ các công ty, tổ chức hàng đầu có mong muốn chia sẻ kiến thức chuyên môn, xây dựng thương hiệu cá nhân, và kiến tạo các khóa học kỹ năng thực chiến chất lượng.',
    image: '/images/BEC-talent-2.png',
    background: '/images/Rec-2.png',
    icon: '/images/bec-icon-2.svg',
    activeIcon: '/images/bec-icon-2a.svg',
    sticker1: '/images/bec-sticker-3.svg',
    sticker2: '/images/bec-sticker-4.svg',
    foreground: '/images/bec-foreground-2.png',
    sticker1Size: { desktop: { width: 80, height: 80 }, mobile: { width: 80, height: 80 } },
    sticker2Size: { desktop: { width: 121, height: 100 }, mobile: { width: 98, height: 80 } },
  },
  {
    id: 'advance',
    title: 'Bậc thầy Tiên Phong',
    content:
      'Bạn là **giáo viên, giảng viên** với kiến thứ c sâu rộng và kinh nghiệm sư phạm lâu năm, có mong muốn số hóa bài giảng, chia sẻ tri thức đến hàng ngàn học viên và trở thành người dẫn đầu giáo dục số.',
    image: '/images/BEC-talent-3.png',
    background: '/images/Rec-3.png',
    icon: '/images/bec-icon-3.svg',
    activeIcon: '/images/bec-icon-3a.svg',
    sticker1: '/images/bec-sticker-5.svg',
    sticker2: '/images/bec-sticker-6.svg',
    foreground: '/images/bec-foreground-3.png',
    sticker1Size: { desktop: { width: 80, height: 80 }, mobile: { width: 80, height: 80 } },
    sticker2Size: { desktop: { width: 133, height: 127 }, mobile: { width: 108, height: 103 } },
  },
];

export const BEC: React.FC<BECProps> = ({ className = '' }) => {
  return (
    <section className={`bg-gray-50 py-16 sm:py-20 lg:py-24 ${className}`}>
      <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
        <div className="mb-12 text-center sm:mb-16 lg:mb-20">
          <div className="hidden md:block">
            <h2 className="mb-4 text-headline-lg font-semibold text-primary_text md:text-display-md md:font-bold">
              Ai cũng có thể trở thành
            </h2>
            <h3 className="mb-6 text-headline-lg font-semibold text-primary-500 md:text-display-md md:font-bold">
              Edu-Creator
            </h3>
          </div>
          <div className="block md:hidden">
            <h2 className="mb-4 text-headline-lg font-semibold text-primary_text md:text-display-md md:font-bold">
              Ai cũng có thể trở thành <span className="text-primary-500">Edu-Creator</span>
            </h2>
          </div>
          <p className="mx-auto max-w-2xl text-label-lg font-medium leading-6 text-secondary_text md:px-16 md:text-headline-xs md:font-semibold md:leading-7">
            Mỗi người đều có điều đáng để chia sẻ - kỹ năng, chuyên môn, hay một góc nhìn riêng biệt.
          </p>
        </div>

        <div className="grid grid-cols-1 items-start gap-12 lg:grid-cols-2 lg:gap-16">
          <div className="space-y-4">
            {tabData.map((tab, index) => (
              <div
                key={tab.id}
                id={`tab-${tab.id}`}
                className={`overflow-hidden rounded-2xl bg-white transition-shadow duration-200`}
              >
                <div className="flex w-full items-center justify-between p-6">
                  <div className="flex items-center">
                    <div className="relative size-10">
                      <Image
                        id={`tab-icon-inactive-${tab.id}`}
                        src={tab.icon}
                        alt={tab.title}
                        width={40}
                        height={40}
                        className={`absolute inset-0 transition-opacity duration-300 ease-in-out ${
                          index === 0 ? 'opacity-0' : 'opacity-100'
                        }`}
                      />
                      <Image
                        id={`tab-icon-active-${tab.id}`}
                        src={tab.activeIcon}
                        alt={tab.title}
                        width={40}
                        height={40}
                        className={`absolute inset-0 transition-opacity duration-300 ease-in-out ${
                          index === 0 ? 'opacity-100' : 'opacity-0'
                        }`}
                      />
                    </div>
                    <h4 className="ml-2 text-headline-xs text-primary_text">{tab.title}</h4>
                  </div>
                </div>

                <div
                  id={`tab-content-${tab.id}`}
                  className="relative bottom-5 px-6 pb-6 pt-0"
                  style={{ display: index === 0 ? 'block' : 'none' }}
                >
                  <div className="pl-12">
                    <p className="font-nunito text-body-lg leading-6 text-[#21272A]">
                      {typeof tab.content === 'string'
                        ? tab.content
                            .split(/\*\*(.*?)\*\*/g)
                            .map((part, i) => (i % 2 === 1 ? <strong key={i}>{part}</strong> : part))
                        : tab.content}
                    </p>
                  </div>
                </div>
              </div>
            ))}
          </div>

          <div className="justify-end lg:flex">
            <div className="relative w-full max-w-md lg:max-w-lg">
              <div
                id="bec-dynamic-image"
                className="relative mx-auto flex size-[280px] items-center justify-center sm:size-[360px]"
              >
                {/* Background Images - All preloaded */}
                {tabData.map((tab, index) => (
                  <Image
                    key={`bg-${tab.id}`}
                    id={`bec-background-image-${index}`}
                    src={tab.background}
                    alt="Background"
                    width={360}
                    height={360}
                    className={`absolute inset-0 z-[1] h-full w-full object-contain transition-opacity duration-300 ${
                      index === 0 ? 'opacity-100' : 'opacity-0'
                    }`}
                    priority={index === 0}
                  />
                ))}

                {/* Foreground Images - All preloaded */}
                {tabData.map((tab, index) => (
                  <div
                    key={`fg-${tab.id}`}
                    id={`bec-foreground-container-${index}`}
                    className={`absolute inset-0 z-[2] flex items-center justify-center transition-opacity duration-300 ${
                      index === 0 ? 'opacity-100' : 'opacity-0'
                    }`}
                    style={{ transition: 'all 0.8s ease-in-out' }}
                  >
                    <Image
                      id={`bec-foreground-image-${index}`}
                      src={tab.foreground}
                      alt={tab.title}
                      width={320}
                      height={320}
                      className="size-[245px] object-contain sm:size-[315px]"
                      priority={index === 0}
                    />
                  </div>
                ))}

                {/* Talent Images - All preloaded */}
                {tabData.map((tab, index) => (
                  <div
                    key={`talent-${tab.id}`}
                    className={`absolute inset-0 z-[3] flex items-center justify-center transition-opacity duration-300 ${
                      index === 0 ? 'opacity-100' : 'opacity-0'
                    }`}
                  >
                    <Image
                      id={`bec-talent-image-${index}`}
                      src={tab.image}
                      alt={tab.title}
                      width={360}
                      height={360}
                      className="size-[280px] object-contain sm:size-[360px]"
                      priority={index === 0}
                      quality={95}
                      sizes="(max-width: 640px) 280px, 360px"
                    />
                  </div>
                ))}

                {/* Sticker1 Images - All preloaded */}
                {tabData.map((tab, index) => (
                  <div
                    key={`sticker1-${tab.id}`}
                    id={`bec-sticker1-container-${index}`}
                    className={`absolute z-[4] transform transition-opacity duration-300 ${
                      index === 0 ? 'left-0 -translate-x-1/2 opacity-100' : 'left-0 top-1/4 -translate-x-1/2 opacity-0'
                    }`}
                    style={{
                      transition: 'all 0.8s ease-in-out',
                      top: index === 0 ? '15%' : undefined,
                      width: `${tab.sticker1Size.mobile.width}px`,
                      height: `${tab.sticker1Size.mobile.height}px`,
                    }}
                  >
                    <style>{`
                      @media (min-width: 640px) {
                        #bec-sticker1-container-${index} {
                          width: ${tab.sticker1Size.desktop.width}px !important;
                          height: ${tab.sticker1Size.desktop.height}px !important;
                        }
                      }
                    `}</style>
                    <Image
                      id={`bec-sticker1-image-${index}`}
                      src={tab.sticker1}
                      alt="Left Sticker"
                      width={tab.sticker1Size.desktop.width}
                      height={tab.sticker1Size.desktop.height}
                      className="size-full object-contain"
                      quality={95}
                      sizes={`(max-width: 640px) ${tab.sticker1Size.mobile.width}px, ${tab.sticker1Size.desktop.width}px`}
                    />
                  </div>
                ))}

                {/* Sticker2 Images - All preloaded */}
                {tabData.map((tab, index) => (
                  <div
                    key={`sticker2-${tab.id}`}
                    id={`bec-sticker2-container-${index}`}
                    className={`absolute z-[4] transform transition-opacity duration-300 ${
                      index === 0 ? 'right-0 translate-x-1/2 opacity-100' : 'right-0 top-3/4 translate-x-1/2 opacity-0'
                    }`}
                    style={{
                      transition: 'all 0.8s ease-in-out',
                      top: index === 0 ? '80%' : undefined,
                      width: `${tab.sticker2Size.mobile.width}px`,
                      height: `${tab.sticker2Size.mobile.height}px`,
                    }}
                  >
                    <style>{`
                      @media (min-width: 640px) {
                        #bec-sticker2-container-${index} {
                          width: ${tab.sticker2Size.desktop.width}px !important;
                          height: ${tab.sticker2Size.desktop.height}px !important;
                        }
                      }
                    `}</style>
                    <Image
                      id={`bec-sticker2-image-${index}`}
                      src={tab.sticker2}
                      alt="Right Sticker"
                      width={tab.sticker2Size.desktop.width}
                      height={tab.sticker2Size.desktop.height}
                      className="size-full object-contain"
                      quality={95}
                      sizes={`(max-width: 640px) ${tab.sticker2Size.mobile.width}px, ${tab.sticker2Size.desktop.width}px`}
                    />
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>

        <BECInteractive tabData={tabData} />
      </div>
    </section>
  );
};

export default BEC;
