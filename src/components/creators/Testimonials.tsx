import { Typography } from '@/components/ui';
import { routePaths } from '@/config/constant';
import { userServices } from '@/services/user.services';
import Image from 'next/image';
import React from 'react';
import { TestimonialsInteractive } from './TestimonialsInteractive';

interface TestimonialsProps {
  className?: string;
}

interface Testimonial {
  id: string;
  name: string;
  title: string;
  company: string;
  content: string;
  avatar: string;
  ctaText: string;
}

const testimonials: Testimonial[] = [
  {
    id: 'nguyen-trung-hung',
    name: '<PERSON><PERSON><PERSON><PERSON> Trung Hưng',
    title: 'Founder Human Design Vietnam',
    company: 'Human Design Vietnam',
    content:
      'Chương trình CCP đã giúp Hưng nhìn lại cách mình truyền đạt và cải thiện cách diễn đạt ngôn gữ sức tích. Nhờ các buổi brainstorming và networking, Hưng không chỉ nhận được nhiều ý tưởng mới mà còn được tiếp thêm rất nhiều động lực để tiếp tục sáng tạo.',
    avatar: '/images/rating-p1.svg',
    ctaText: 'Bắt đầu hành trình của bạn',
  },
  {
    id: 'tran-kim-chi',
    name: 'Trần Kim Chi',
    title: 'Huấn luyện viên quốc tế, Diễn giả truyền cảm hứng và Nhà sáng lập iChangeCenter (itv)',
    company: 'Nhà sáng lập iChangeCenter (trv)',
    content:
      'Chi đã hoàn thành khóa học đầu tiên và chính thức trở thành Edu-Creator của Studify chỉ sau 30 ngày – Và Chi không hề đơn độc trong hành trình ấy.',
    avatar: '/images/rating-p2.svg',
    ctaText: 'Bắt đầu hành trình của bạn',
  },
  {
    id: 'nguyen-minh-nhat',
    name: 'Nguyễn Minh Nhật',
    title: 'Nhà giáo dục Tài chính cá nhân',
    company: 'EduTech Solutions',
    content:
      'Cảm giác với Studify là gần gũi, thân thiện, cảm hứng để làm và duy trì. Mình thấy các bạn làm được cho cả người xem/học lẫn người sáng tạo nội dung giáo dục (edu-creator).',
    avatar: '/images/rating-p3.svg',
    ctaText: 'Bắt đầu hành trình của bạn',
  },
];

export const Testimonials: React.FC<TestimonialsProps> = async ({ className = '' }) => {
  const { isLoggedIn } = await userServices();
  return (
    <section className={`bg-[#F4F6F9] py-16 sm:py-20 lg:py-24 ${className}`}>
      <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
        <div className="mb-12 text-center sm:mb-16 lg:mb-20">
          <h2 className="mb-6 text-headline-lg text-primary_text md:text-display-md">
            Edu-Creator của Studify nói gì?
          </h2>
        </div>

        <div className="relative">
          <button
            id="testimonials-prev"
            className="absolute left-0 top-1/2 z-10 -translate-x-4 -translate-y-1/2 rounded-full bg-white p-2 transition-all duration-200 hover:shadow-dark sm:-translate-x-6 sm:p-3 lg:-translate-x-8"
            aria-label="Previous testimonial"
          >
            <svg className="h-6 w-6 text-black" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
            </svg>
          </button>

          <button
            id="testimonials-next"
            className="absolute right-0 top-1/2 z-10 -translate-y-1/2 translate-x-4 rounded-full bg-white p-2 transition-all duration-200 hover:shadow-dark sm:translate-x-6 sm:p-3 lg:translate-x-8"
            aria-label="Next testimonial"
          >
            <svg className="h-6 w-6 text-black" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
            </svg>
          </button>

          <div className="overflow-hidden">
            <div
              id="testimonials-slider"
              className="flex transition-transform duration-500 ease-in-out"
              style={{ transform: 'translateX(0%)' }}
            >
              {Array.from({ length: Math.max(1, testimonials.length - 1) }, (_, slideIndex) => (
                <div
                  key={`slide-${slideIndex}`}
                  className="w-full flex-shrink-0 px-4 sm:px-6 lg:px-8"
                  style={{ minWidth: '100%' }}
                >
                  <div className="hidden sm:grid sm:grid-cols-2 sm:gap-8 lg:gap-12">
                    {testimonials.slice(slideIndex, slideIndex + 2).map((testimonial) => (
                      <div key={testimonial.id} className="flex h-[410px] w-[540px] flex-col rounded-3xl bg-white p-8">
                        <div className="flex flex-1 flex-col items-center justify-center text-center">
                          <div className="mb-6 flex flex-col items-center">
                            <Image src={testimonial.avatar} alt={testimonial.name} width={80} height={80} />
                            <div className="relative top-4">
                              <Typography variant="headlineXs" className="text-primary_text">
                                {testimonial.name}
                              </Typography>
                              <div className="flex flex-col gap-2">
                                <Typography variant="bodyLg" className="font-nunito text-[#21272A]">
                                  {testimonial.title}
                                </Typography>
                              </div>
                            </div>
                          </div>

                          <div className="relative top-4 mb-6 flex-1">
                            <Typography variant="bodyLg" className="font-nunito text-[#21272A]">
                              {testimonial.content}
                            </Typography>
                          </div>

                          <div>
                            <a
                              href={isLoggedIn ? routePaths.profile.children.creator.path : routePaths.login}
                              className="inline-flex items-center text-label-lg text-primary-500"
                            >
                              {testimonial.ctaText}
                              <svg className="ml-2 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path
                                  strokeLinecap="round"
                                  strokeLinejoin="round"
                                  strokeWidth={2}
                                  d="M17 8l4 4m0 0l-4 4m4-4H3"
                                />
                              </svg>
                            </a>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>

                  <div className="sm:hidden">
                    {testimonials.slice(slideIndex, slideIndex + 1).map((testimonial) => (
                      <div key={testimonial.id} className="rounded-3xl bg-white p-6">
                        <div className="flex flex-col items-center justify-center text-center">
                          <div className="mb-8 flex flex-col items-center">
                            <Image src={testimonial.avatar} alt={testimonial.name} width={80} height={80} />
                            <div className="flex h-full w-full items-center justify-center"></div>
                            <div className="relative top-4">
                              <Typography variant="headlineXs" className="text-primary_text">
                                {testimonial.name}
                              </Typography>
                              <div className="flex flex-col gap-2">
                                <Typography variant="bodyLg" className="font-nunito text-[#21272A]">
                                  {testimonial.title}
                                </Typography>
                              </div>
                            </div>
                          </div>

                          <div className="mb-6">
                            <Typography variant="bodyLg" className="font-nunito text-[#21272A]">
                              {testimonial.content}
                            </Typography>
                          </div>

                          <div>
                            <a
                              href={isLoggedIn ? routePaths.profile.children.creator.path : routePaths.login}
                              className="inline-flex items-center text-label-lg text-primary-500"
                            >
                              {testimonial.ctaText}
                              <svg className="ml-2 h-3 w-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path
                                  strokeLinecap="round"
                                  strokeLinejoin="round"
                                  strokeWidth={2}
                                  d="M17 8l4 4m0 0l-4 4m4-4H3"
                                />
                              </svg>
                            </a>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        <TestimonialsInteractive testimonials={testimonials} />
      </div>
    </section>
  );
};

export default Testimonials;
