import { routePaths } from '@/config/constant';
import { userServices } from '@/services/user.services';
import { Button } from 'antd';
import Link from 'next/link';
import React from 'react';
import { DemoInteractive } from './DemoInteractive';

interface DemoProps {
  className?: string;
}

interface DemoStep {
  id: string;
  title: string;
  description: string;
  video: string;
}

const demoSteps: DemoStep[] = [
  {
    id: 'step1',
    title: 'Thiết lập',
    description: 'Đặt tên, viết mô tả, chọn chủ đề – khởi động khóa học của bạn.',
    video: '/videos/demo-step-1.mp4',
  },
  {
    id: 'step2',
    title: 'Thiế<PERSON> kế',
    description: 'Tải video, sắp xếp nội dung, kéo-thả bài tập, câu hỏi, tài liệu học.',
    video: '/videos/demo-step-2.mp4',
  },
  {
    id: 'step3',
    title: '<PERSON><PERSON><PERSON> bản',
    description: '<PERSON><PERSON><PERSON> xét duyệt và phát hành khóa học lên nền tảng Studify.',
    video: '/videos/demo-step-3.mp4',
  },
];

export const Demo: React.FC<DemoProps> = async ({ className = '' }) => {
  const { isLoggedIn } = await userServices();
  return (
    <section className={`bg-[#F4F6F9] py-16 sm:py-20 lg:py-24 ${className}`}>
      <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
        <div className="mb-12 text-center sm:mb-16 lg:mb-20">
          <div className="mb-2 md:mb-4">
            <p className="text-body-md font-medium tracking-wider text-primary-500 sm:text-body-lg">
              EDU-CREATOR STUDIO
            </p>
          </div>
          <h2 className="text-headline-lg font-semibold text-[#21272A] md:mb-4 md:text-display-md">3 Bước đơn giản</h2>
          <h3 className="text-headline-lg font-semibold text-[#21272A] md:mb-4 md:text-display-md">
            Biến tri thức thành <span className="block sm:inline">di sản</span>
          </h3>
        </div>

        <div className="mb-8 sm:mb-12">
          <div className="hidden justify-center pl-20 sm:flex">
            <div className="flex items-start space-x-8 lg:space-x-12">
              {demoSteps.map((step, index) => (
                <div key={step.id} className="flex flex-col">
                  <div className="relative right-8 flex items-center">
                    <div
                      id={`demo-step-${step.id}`}
                      className={`flex h-8 w-8 items-center justify-center rounded-full text-body-md transition-all duration-300`}
                    >
                      {index + 1}
                    </div>
                    <p id={`demo-step-title-${step.id}`} className={`ml-4 text-label-lg`}>
                      {step.title}
                    </p>
                    {index <= 1 && (
                      <div
                        data-connecting-line
                        className="absolute left-32 top-1/2 h-0.5 w-48 -translate-y-1/2 transform"
                      />
                    )}
                  </div>

                  <div className="ml-4">
                    <p id={`demo-step-description-${step.id}`} className="mt-1 max-w-72 font-nunito text-body-lg">
                      {step.description}
                    </p>
                  </div>
                </div>
              ))}
            </div>
          </div>

          <div className="space-y-4 sm:hidden">
            {demoSteps.map((step, index) => (
              <div key={step.id} className="relative flex items-start space-x-4">
                <div className="relative flex flex-col items-center">
                  <div
                    id={`demo-step-${step.id}`}
                    className={`flex h-8 w-8 flex-shrink-0 items-center justify-center rounded-full text-body-md transition-all duration-300`}
                  >
                    {index + 1}
                  </div>
                  {index <= 1 && <div data-connecting-line-vertical className="bg-gray-300 mt-2 h-8 w-0.5" />}
                </div>
                <div className="flex-1 pt-1">
                  <p id={`demo-step-title-${step.id}`} className={`text-body-lg`}>
                    {step.title}
                  </p>
                  <p id={`demo-step-description-${step.id}`} className="mt-1 font-nunito text-body-md">
                    {step.description}
                  </p>
                </div>
              </div>
            ))}
          </div>
        </div>

        <div className="video-demo-container mx-auto max-w-[74rem]">
          <div className="relative overflow-hidden rounded-3xl bg-neutral-100 shadow-medium">
            <div className="relative aspect-video w-full">
              {demoSteps.map((step, index) => (
                <video
                  key={step.id}
                  id={`demo-video-${index}`}
                  className={`absolute inset-0 h-full w-full object-cover transition-opacity duration-300 ${
                    index === 0 ? 'opacity-100' : 'opacity-0'
                  }`}
                  muted
                  playsInline
                  preload={index === 0 ? 'metadata' : 'none'}
                >
                  <source src={step.video} type="video/mp4" />
                  Your browser does not support the video tag.
                </video>
              ))}
            </div>
          </div>
        </div>
        <div className="mt-8 flex justify-center">
          <Link href={isLoggedIn ? routePaths.profile.children.creator.path : routePaths.login}>
            <Button type="primary" className="rounded-xl px-8 py-3 font-medium shadow-md">
              Trải nghiệm ngay!
            </Button>
          </Link>
        </div>

        <DemoInteractive demoSteps={demoSteps} />
      </div>
    </section>
  );
};

export default Demo;
