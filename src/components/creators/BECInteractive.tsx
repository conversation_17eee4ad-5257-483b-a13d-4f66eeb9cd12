'use client';

import { useEffect } from 'react';

interface TabContent {
  id: string;
  title: string;
  content: string;
  image: string;
  background: string;
  icon: string;
  activeIcon: string;
  sticker1: string;
  sticker2: string;
  foreground: string;
  sticker1Size: {
    desktop: { width: number; height: number };
    mobile: { width: number; height: number };
  };
  sticker2Size: {
    desktop: { width: number; height: number };
    mobile: { width: number; height: number };
  };
}

interface BECInteractiveProps {
  tabData: TabContent[];
}

const INTERVAL_TIME = 6000;

export const BECInteractive: React.FC<BECInteractiveProps> = ({ tabData }) => {
  useEffect(() => {
    let currentIndex = 0;
    let interval: NodeJS.Timeout;

    const switchToTab = (targetIndex: number) => {
      tabData.forEach((tab, index) => {
        const contentElement = document.getElementById(`tab-content-${tab.id}`);

        if (contentElement) {
          contentElement.style.display = index === targetIndex ? 'block' : 'none';
        }

        const inactiveIconElement = document.getElementById(`tab-icon-inactive-${tab.id}`);
        const activeIconElement = document.getElementById(`tab-icon-active-${tab.id}`);

        if (inactiveIconElement && activeIconElement) {
          if (index === targetIndex) {
            inactiveIconElement.className = inactiveIconElement.className.replace('opacity-100', 'opacity-0');
            activeIconElement.className = activeIconElement.className.replace('opacity-0', 'opacity-100');
          } else {
            inactiveIconElement.className = inactiveIconElement.className.replace('opacity-0', 'opacity-100');
            activeIconElement.className = activeIconElement.className.replace('opacity-100', 'opacity-0');
          }
        }

        // Handle shadow-light class for active tab
        const tabElement = document.getElementById(`tab-${tab.id}`);
        if (tabElement) {
          if (index === targetIndex) {
            // Add shadow-light class if not present
            if (!tabElement.className.includes('creator-bec-tab-shadow')) {
              tabElement.className += ' creator-bec-tab-shadow';
            }
          } else {
            // Remove shadow-light class if present
            tabElement.className = tabElement.className.replace('creator-bec-tab-shadow', '').trim();
          }
        }
      });

      // Hide all image layers first, then show the target ones
      tabData.forEach((_, index) => {
        // Background images
        const backgroundImage = document.getElementById(`bec-background-image-${index}`);
        if (backgroundImage) {
          backgroundImage.className = backgroundImage.className.replace('opacity-100', 'opacity-0');
        }

        // Foreground containers
        const foregroundContainer = document.getElementById(`bec-foreground-container-${index}`);
        if (foregroundContainer) {
          foregroundContainer.className = foregroundContainer.className.replace('opacity-100', 'opacity-0');
          // Reset any inline styles that might override the opacity class
          foregroundContainer.style.removeProperty('opacity');
          foregroundContainer.style.removeProperty('transform');
        }

        // Talent images
        const talentContainer = document.getElementById(`bec-talent-image-${index}`)?.parentElement;
        if (talentContainer) {
          talentContainer.className = talentContainer.className.replace('opacity-100', 'opacity-0');
        }

        // Sticker containers
        const sticker1Container = document.getElementById(`bec-sticker1-container-${index}`);
        if (sticker1Container) {
          sticker1Container.className = sticker1Container.className.replace('opacity-100', 'opacity-0');
        }

        const sticker2Container = document.getElementById(`bec-sticker2-container-${index}`);
        if (sticker2Container) {
          sticker2Container.className = sticker2Container.className.replace('opacity-100', 'opacity-0');
        }
      });

      // Show the target tab's images
      const targetTab = tabData[targetIndex];
      if (targetTab) {
        const degree = targetIndex === 2 ? 58 : targetIndex === 1 ? 90 : 45;
        // Show background image
        const targetBackgroundImage = document.getElementById(`bec-background-image-${targetIndex}`);
        if (targetBackgroundImage) {
          targetBackgroundImage.className = targetBackgroundImage.className.replace('opacity-0', 'opacity-100');
        }

        // Show and animate foreground image
        const targetForegroundContainer = document.getElementById(`bec-foreground-container-${targetIndex}`);
        if (targetForegroundContainer) {
          targetForegroundContainer.className = targetForegroundContainer.className.replace('opacity-0', 'opacity-100');

          // Apply animation
          targetForegroundContainer.style.setProperty('transition', 'all 0.4s linear', 'important');
          targetForegroundContainer.style.setProperty('opacity', '0', 'important');

          setTimeout(() => {
            targetForegroundContainer.style.setProperty('transform', `rotate(${degree}deg)`, 'important');
            targetForegroundContainer.style.setProperty('opacity', '1', 'important');

            // setTimeout(() => {
            //   targetForegroundContainer.style.setProperty('opacity', '1', 'important');
            //   targetForegroundContainer.style.setProperty('transform', 'rotate(0deg) scale(1)', 'important');
            // }, 500);
          }, 200);
        }

        // Show talent image
        const targetTalentContainer = document.getElementById(`bec-talent-image-${targetIndex}`)?.parentElement;
        if (targetTalentContainer) {
          targetTalentContainer.className = targetTalentContainer.className.replace('opacity-0', 'opacity-100');
        }

        // Show and animate the target tab's stickers
        const targetSticker1Container = document.getElementById(`bec-sticker1-container-${targetIndex}`);
        const targetSticker2Container = document.getElementById(`bec-sticker2-container-${targetIndex}`);

        // Animate Sticker1
        if (targetSticker1Container) {
          const sticker1Positions = [
            { left: '15%', top: '60%' },
            { right: '-20%', top: '55%' },
            { right: '-12%', top: '45%' },
          ];

          const sticker1StartPositions = [
            { right: '0', top: '45%' },
            { left: '15%', top: '60%' },
            { right: '-20%', top: '55%' },
          ];

          const sticker1StartPosition = sticker1StartPositions[targetIndex];
          const sticker1Position = sticker1Positions[targetIndex];

          // Show the sticker first
          targetSticker1Container.className = targetSticker1Container.className.replace('opacity-0', 'opacity-100');

          targetSticker1Container.style.setProperty('transition', 'none', 'important');
          targetSticker1Container.style.setProperty('top', sticker1StartPosition.top, 'important');

          if (sticker1StartPosition.right !== undefined) {
            targetSticker1Container.style.setProperty('right', sticker1StartPosition.right, 'important');
            targetSticker1Container.style.setProperty('left', 'auto', 'important');
          } else if (sticker1StartPosition.left !== undefined) {
            targetSticker1Container.style.setProperty('left', sticker1StartPosition.left, 'important');
            targetSticker1Container.style.setProperty('right', 'auto', 'important');
          }

          // After a brief delay, animate to final position
          setTimeout(() => {
            targetSticker1Container.style.setProperty('transition', 'all 0.8s ease-in-out', 'important');
            targetSticker1Container.style.setProperty('top', sticker1Position.top, 'important');

            if (sticker1Position.left !== undefined) {
              targetSticker1Container.style.setProperty('left', sticker1Position.left, 'important');
              targetSticker1Container.style.setProperty('right', 'auto', 'important');
            } else {
              targetSticker1Container.style.setProperty('right', sticker1Position.right, 'important');
              targetSticker1Container.style.setProperty('left', 'auto', 'important');
            }
          }, 100);
        }

        // Animate Sticker2
        if (targetSticker2Container) {
          const sticker2Positions = [
            { right: '10%', top: '20%' },
            { left: '-25%', top: '15%' },
            { left: '-15%', top: '-15%' },
          ];

          const sticker2StartPositions = [
            { left: '15%', top: '0%' },
            { right: '10%', top: '20%' },
            { left: '-25%', top: '15%' },
          ];
          const sticker2Position = sticker2Positions[targetIndex];
          const sticker2StartPosition = sticker2StartPositions[targetIndex];

          // Show the sticker first
          targetSticker2Container.className = targetSticker2Container.className.replace('opacity-0', 'opacity-100');

          // Start from start position
          targetSticker2Container.style.setProperty('transition', 'none', 'important');

          if (sticker2StartPosition.right !== undefined) {
            targetSticker2Container.style.setProperty('right', sticker2StartPosition.right, 'important');
            targetSticker2Container.style.setProperty('left', 'auto', 'important');
          } else if (sticker2StartPosition.left !== undefined) {
            targetSticker2Container.style.setProperty('left', sticker2StartPosition.left, 'important');
            targetSticker2Container.style.setProperty('right', 'auto', 'important');
          }
          targetSticker2Container.style.setProperty('top', sticker2StartPosition.top || 'auto', 'important');

          // After a brief delay, animate to final position
          setTimeout(() => {
            targetSticker2Container.style.setProperty('transition', 'all 0.8s linear', 'important');
            targetSticker2Container.style.setProperty('top', sticker2Position.top || 'auto', 'important');

            if (sticker2Position.right !== undefined) {
              targetSticker2Container.style.setProperty('right', sticker2Position.right, 'important');
              targetSticker2Container.style.setProperty('left', 'auto', 'important');
            } else if (sticker2Position.left !== undefined) {
              targetSticker2Container.style.setProperty('left', sticker2Position.left, 'important');
              targetSticker2Container.style.setProperty('right', 'auto', 'important');
            }
          }, 100);
        }
      }

      currentIndex = targetIndex;
    };

    const startAutoSwitching = () => {
      if (interval) {
        clearInterval(interval);
      }

      interval = setInterval(() => {
        const nextIndex = (currentIndex + 1) % tabData.length;
        switchToTab(nextIndex);
      }, INTERVAL_TIME);
    };

    const stopAutoSwitching = () => {
      if (interval) {
        clearInterval(interval);
      }
    };

    tabData.forEach((tab, index) => {
      const tabElement = document.getElementById(`tab-${tab.id}`);
      if (tabElement) {
        tabElement.style.cursor = 'pointer';
        tabElement.addEventListener('click', () => {
          stopAutoSwitching();

          switchToTab(index);

          setTimeout(() => {
            startAutoSwitching();
          }, INTERVAL_TIME);
        });
      }
    });

    setTimeout(() => {
      switchToTab(0);
    }, 500);

    startAutoSwitching();

    return () => {
      stopAutoSwitching();
    };
  }, [tabData]);

  return null;
};
