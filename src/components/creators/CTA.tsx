import { routePaths } from '@/config/constant';
import { userServices } from '@/services/user.services';
import { Button } from 'antd';
import Image from 'next/image';
import Link from 'next/link';

const CTA = async () => {
  const { isLoggedIn } = await userServices();
  return (
    <section className="relative sm:py-20 lg:py-24">
      <div className="mx-auto max-w-7xl sm:px-6 lg:px-8">
        <div className="relative h-[500px] w-full overflow-hidden sm:h-[600px]">
          <Image
            width={1140}
            height={502}
            src="/images/CTA-bg.png"
            alt="CTA Background"
            className="h-full w-full object-cover sm:object-contain"
          />

          <div className="absolute inset-0 flex items-center justify-center">
            <div className="max-w-4xl px-6 text-center">
              <h2 className="mb-4 text-headline-lg text-white md:px-8 md:text-display-md">
                <PERSON><PERSON> danh vào thế hệ Edu-Creators tiên phong!
              </h2>

              <p className="mb-8 text-body-lg text-primary_text_reversed md:px-24 md:text-headline-xs">
                Biến ý tưởng thành hiện thực, thiết kế bài giảng sáng tạo và kết nối với hàng nghìn người học trên
                Studify.
              </p>
              <Link href={isLoggedIn ? routePaths.profile.children.creator.path : routePaths.login}>
                <Button type="primary" className="rounded-xl font-medium shadow-md">
                  Bắt đầu tạo khóa học
                </Button>
              </Link>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default CTA;
