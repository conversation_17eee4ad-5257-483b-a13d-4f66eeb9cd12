'use client';
import { Button } from 'antd';
import Image from 'next/image';
import React from 'react';
import { FAQInteractive } from './FAQInteractive';

interface FAQProps {
  className?: string;
}

interface FAQItem {
  id: string;
  question: string;
  answer: string;
}

const faqItems: FAQItem[] = [
  {
    id: 'studify-platform',
    question: 'Nền tảng Studify vận hành như thế nào?',
    answer:
      'Studify là Nền tảng giáo dục sáng tạo đầu tiên tại Việt Nam (Vietnam 1st Creative Educational Platform).\n\nStudify tự hào mang đến một sản phẩm giáo dục trực tuyến mới mẻ, hiện đại và toàn diện, nơi kết nối tất cả Người học (Learners) và Người dạy (Edu-Creators) trên cùng một nền tảng (All-in-One). Studify tin rằng gi<PERSON>o dụ<PERSON> không chỉ là việc tiếp nhận kiế<PERSON> thứ<PERSON>, mà còn là hành trình xây dựng một tư duy chủ động, sáng tạo, và liên tục phát triển.\n\nĐồng thời Studify cũng tự hào mang đến cho người dùng mô hình Subscription bằng việc cung cấp các gói dịch vụ linh hoạt như:\n\n1) Gói Free: Trải nghiệm miễn phí giới hạn, giúp khách hàng làm quen với nền tảng.\n\n2) Gói Premium: Truy cập đầy đủ các khóa học, bài học và tính năng nâng cao với mức phí cố định hàng tháng/năm.',
  },
  {
    id: 'why-choose-studify',
    question: 'Tại sao nên lựa chọn nền tảng Studify?',
    answer:
      'Là Người học (Learner) trên Studify tôi được trao quyền:\n\n1) Học tập mọi lúc mọi nơi - Tiếp cận các khóa học chất lượng cao, đa dạng và linh hoạt trên mọi thiết bị điện tử.\n\n2) Học thứ mình thích học - Tận hưởng trải nghiệm học tập cá nhân hóa, phù hợp với năng lực và tốc độ học tập riêng của bản thân.\n\n3) Tương tác và đồng hành - Tối ưu trải nghiệm và giá trị học tập với các tính năng tương tác hai chiều đa dạng.\n\nLà Người dạy (Edu-Creator) trên Studify tôi được trao quyền:\n\n1) Sáng tạo không giới hạn - Tự do thể hiện sự sáng tạo, mang dấu ấn cá nhân vào từng bài giảng để tạo ấn tượng mạnh mẽ với người học.\n\n2) Xây dựng thương hiệu cá nhân - Cơ hội mở rộng mạng lưới cộng tác và trở thành người tiên phong trong cộng đồng giáo dục sáng tạo tại Việt Nam.\n\n3) Tạo thu nhập bền vững - Chuyển hóa kiến thức và kỹ năng cá nhân thành nguồn thu nhập lâu dài, ổn định.',
  },
  {
    id: 'platform-groups',
    question: 'Nội dung trên Nền tảng thuộc những nhóm chủ đề nào?',
    answer:
      'Các khoá học được phân loại theo nhiều lĩnh vực nhằm phục vụ đa dạng đối tượng người học. Các lĩnh vực chủ đề chính bao gồm nhưng không giới hạn:\n\n1) Khoa học Tự nhiên\n\n2) Khoa học Xã hội\n\n3) Khoa học Máy tính và Công nghệ\n\n4) Kinh doanh\n\n5) Nghệ thuật và Truyền thông\n\n6) Phát triển chuyên môn\n\n7) Sức khỏe và Đời sống\n\nTham khảo thêm tại [https://studify.vn/](https://studify.vn/), mục Khóa học nổi bật.',
  },
  {
    id: 'revenue-sharing',
    question: 'Nội dung được kiểm duyệt như thế nào?',
    answer:
      'Studify xét duyệt nội dung để đảm bảo mọi khóa học, bài giảng hay nội dung giáo dục được xuất bản trên nền tảng đều chất lượng – an toàn – có giá trị học thuật và ứng dụng thực tiễn.',
  },
  {
    id: 'income-collection',
    question: 'Studify hỗ trợ gì trong việc tạo thu nhập từ khóa học?',
    answer:
      'Chính sách Kiếm tiền cho Edu-Creators hiện tại chỉ được chia sẻ nội bộ và kích hoạt riêng với nhóm các Edu-Creators đã tham gia, và hoàn thành các chương trình như Course [Course Creation Program](https://docs.google.com/presentation/d/e/2PACX-1vQEcQ4jYyzbza0zDiwIZfVZO0Qftbw2a0rzhdQdDU-L3HYaeyzQXYxDS235-VTaMzzwOPf6epRdE5j8/pub?start=false&loop=false&delayms=3000) hoặc [Course Creation Review](https://docs.google.com/presentation/d/e/2PACX-1vTBTaIFntrCDJgjG67UeMrYQg4gHiJRuWwmtAb4N6bnNWpIIzFugI9KoEZrArS_Ki_uE9fatuYbcAzD/pub?start=false&loop=false&delayms=3000) của Studify.\n\nKhi bạn đồng hành cùng Studify ở vai trò Người dạy được chứng nhận bởi Studify (Certified Edu-Creator), bạn sẽ không cần phải chi trả bất kỳ chi phí gói dịch vụ trong hành trình một năm đồng hành cùng chúng tôi. Thay vào đó, bạn sẽ được bật kiếm tiền thông qua các khóa học được xuất bản thành công của mình. Studify sẽ thực hiện việc khấu trừ các khoản phí bao gồm: Phí vận hành nền tảng và dịch vụ; Các khoản thuế phí theo luật định.',
  },
];

export const FAQ: React.FC<FAQProps> = ({ className = '' }) => {
  return (
    <section className={`bg-white py-16 sm:py-20 lg:py-24 ${className}`}>
      <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
        <div className="grid grid-cols-1 gap-12 lg:grid-cols-2 lg:gap-16">
          <div className="lg:sticky lg:top-8 lg:self-start">
            <div className="mb-8">
              <div className="flex items-start md:mb-4">
                <h2 className="text-headline-lg font-semibold text-primary_text sm:text-display-sm md:font-bold lg:text-display-md">
                  Câu hỏi
                </h2>
                <Image
                  src="/images/faq-question.png"
                  alt="FAQ Icon"
                  style={{ marginLeft: 16 }}
                  width={52}
                  height={52}
                  className="h-10 w-10 md:h-[52px] md:w-[52px]"
                />
              </div>
              <h2 className="text-headline-lg font-semibold text-primary_text sm:text-display-sm md:font-bold lg:text-display-md">
                thường gặp
              </h2>
            </div>

            <div className="rounded-2xl bg-[#F4F6F9] p-6">
              <h3 className="mb-3 text-headline-xs text-primary_text md:text-headline-md">Bạn cần thêm thông tin?</h3>
              <p className="mb-4 text-label-lg text-secondary_text">Chúng mình luôn sẵn sàng hỗ trợ!</p>
              <Button
                onClick={() => {
                  window.open('mailto:<EMAIL>');
                }}
                type="primary"
                className="rounded-xl font-medium shadow-md"
              >
                Liên hệ tư vấn
              </Button>
            </div>
          </div>

          <div className="space-y-4">
            {faqItems.map((item, index) => (
              <div key={item.id} className="overflow-hidden rounded-2xl bg-[#F4F6F9]">
                <button
                  id={`faq-button-${item.id}`}
                  className="flex w-full items-center justify-between p-6 text-left transition-colors duration-200"
                  aria-expanded="false"
                  aria-controls={`faq-content-${item.id}`}
                >
                  <h4 className="pr-4 text-label-lg font-medium text-primary_text">{item.question}</h4>
                  <div className="flex-shrink-0">
                    <svg
                      id={`faq-icon-${item.id}`}
                      className="h-6 w-6 transition-transform duration-200"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M12 6v6m0 0v6m0-6h6m-6 0H6"
                      />
                    </svg>
                  </div>
                </button>

                <div
                  id={`faq-content-${item.id}`}
                  className="overflow-hidden transition-all duration-300 ease-in-out"
                  style={{ maxHeight: '0px' }}
                  aria-hidden="true"
                >
                  <div className="px-6 pb-6">
                    <div className="font-nunito text-body-lg text-secondary_text">
                      {item.answer.split('\n\n').map((paragraph, index) => (
                        <p key={index} className="mb-4 last:mb-0">
                          {paragraph.split(/(\[.*?\]\(.*?\))/).map((part, partIndex) => {
                            const linkMatch = part.match(/\[(.*?)\]\((.*?)\)/);
                            if (linkMatch) {
                              return (
                                <a
                                  key={partIndex}
                                  href={linkMatch[2]}
                                  target="_blank"
                                  rel="noopener noreferrer"
                                  className="text-primary-500 underline hover:text-primary-600"
                                >
                                  {linkMatch[1]}
                                </a>
                              );
                            }
                            return part;
                          })}
                        </p>
                      ))}
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        <FAQInteractive faqItems={faqItems} />
      </div>
    </section>
  );
};

export default FAQ;
