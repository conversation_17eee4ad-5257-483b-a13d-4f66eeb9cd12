'use client';

import { useEffect } from 'react';

interface Testimonial {
  id: string;
  name: string;
  title: string;
  company: string;
  content: string;
  avatar: string;
  ctaText: string;
}

interface TestimonialsInteractiveProps {
  testimonials: Testimonial[];
}

export const TestimonialsInteractive: React.FC<TestimonialsInteractiveProps> = ({ testimonials }) => {
  useEffect(() => {
    let currentIndex = 0;
    let autoSlideInterval: NodeJS.Timeout;
    const totalSlides = Math.max(1, testimonials.length - 1);

    const updateSlider = (index: number) => {
      const slider = document.getElementById('testimonials-slider');
      if (slider) {
        slider.style.transform = `translateX(-${index * 100}%)`;
      }

      for (let i = 0; i < totalSlides; i++) {
        const dot = document.getElementById(`testimonial-dot-${i}`);
        if (dot) {
          if (i === index) {
            dot.className = 'h-3 w-3 rounded-full transition-colors duration-200 bg-primary-500';
          } else {
            dot.className = 'h-3 w-3 rounded-full transition-colors duration-200 bg-neutral-300 hover:bg-neutral-400';
          }
        }
      }

      currentIndex = index;
    };

    const nextSlide = () => {
      const nextIndex = (currentIndex + 1) % totalSlides;
      updateSlider(nextIndex);
    };

    const prevSlide = () => {
      const prevIndex = currentIndex === 0 ? totalSlides - 1 : currentIndex - 1;
      updateSlider(prevIndex);
    };

    const startAutoSlide = () => {
      if (autoSlideInterval) {
        clearInterval(autoSlideInterval);
      }
      autoSlideInterval = setInterval(nextSlide, 5000);
    };

    const stopAutoSlide = () => {
      if (autoSlideInterval) {
        clearInterval(autoSlideInterval);
      }
    };

    const prevButton = document.getElementById('testimonials-prev');
    const nextButton = document.getElementById('testimonials-next');

    if (prevButton) {
      prevButton.addEventListener('click', () => {
        stopAutoSlide();
        prevSlide();
        setTimeout(startAutoSlide, 3000);
      });
    }

    if (nextButton) {
      nextButton.addEventListener('click', () => {
        stopAutoSlide();
        nextSlide();
        setTimeout(startAutoSlide, 3000);
      });
    }

    for (let i = 0; i < totalSlides; i++) {
      const dot = document.getElementById(`testimonial-dot-${i}`);
      if (dot) {
        dot.addEventListener('click', () => {
          stopAutoSlide();
          updateSlider(i);
          setTimeout(startAutoSlide, 3000);
        });
      }
    }

    let startX = 0;
    let currentX = 0;
    let isDragging = false;

    const slider = document.getElementById('testimonials-slider');
    if (slider) {
      slider.addEventListener('mousedown', (e) => {
        isDragging = true;
        startX = e.clientX;
        stopAutoSlide();
      });

      slider.addEventListener('mousemove', (e) => {
        if (!isDragging) return;
        currentX = e.clientX;
      });

      slider.addEventListener('mouseup', () => {
        if (!isDragging) return;
        isDragging = false;

        const diffX = startX - currentX;
        if (Math.abs(diffX) > 50) {
          if (diffX > 0) {
            nextSlide();
          } else {
            prevSlide();
          }
        }

        setTimeout(startAutoSlide, 3000);
      });

      slider.addEventListener('touchstart', (e) => {
        isDragging = true;
        startX = e.touches[0].clientX;
        stopAutoSlide();
      });

      slider.addEventListener('touchmove', (e) => {
        if (!isDragging) return;
        currentX = e.touches[0].clientX;
      });

      slider.addEventListener('touchend', () => {
        if (!isDragging) return;
        isDragging = false;

        const diffX = startX - currentX;
        if (Math.abs(diffX) > 50) {
          if (diffX > 0) {
            nextSlide();
          } else {
            prevSlide();
          }
        }

        setTimeout(startAutoSlide, 3000);
      });

      slider.addEventListener('dragstart', (e) => {
        e.preventDefault();
      });
    }

    const container = document.querySelector('.testimonials-container');
    if (container) {
      container.addEventListener('mouseenter', stopAutoSlide);
      container.addEventListener('mouseleave', startAutoSlide);
    }

    startAutoSlide();

    return () => {
      stopAutoSlide();
    };
  }, [testimonials]);

  return null;
};
