'use client';

import { useEffect } from 'react';

interface DemoStep {
  id: string;
  title: string;
  description: string;
  video: string;
}

interface DemoInteractiveProps {
  demoSteps: DemoStep[];
}

export const DemoInteractive: React.FC<DemoInteractiveProps> = ({ demoSteps }) => {
  useEffect(() => {
    let currentStepIndex = 0;
    let hasStarted = false;

    const updateStepUI = (stepIndex: number) => {
      const step = demoSteps[stepIndex];

      demoSteps.forEach((s, index) => {
        const allStepElements = document.querySelectorAll(`[id="demo-step-${s.id}"]`);

        allStepElements.forEach((stepElement) => {
          if (stepElement instanceof HTMLElement) {
            // Remove all possible classes
            stepElement.classList.remove(
              'bg-[#2E2EE5]',
              'bg-transparent',
              'border-2',
              'border-[#B3B3B3]',
              'text-white',
              'text-[#B3B3B3]',
            );

            if (index < stepIndex) {
              // Completed steps - transparent with checkmark
              stepElement.classList.add('bg-transparent');
              stepElement.innerHTML =
                '<svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M16 0.5C24.5604 0.5 31.5 7.43959 31.5 16C31.5 24.5604 24.5604 31.5 16 31.5C7.43959 31.5 0.5 24.5604 0.5 16C0.5 7.43959 7.43959 0.5 16 0.5Z" stroke="#2E2EE5"/><path d="M23.1437 10.25H21.8955C21.7205 10.25 21.5544 10.3304 21.4473 10.4679L14.0848 19.7946L10.5544 15.3214C10.501 15.2536 10.4329 15.1988 10.3553 15.1611C10.2777 15.1233 10.1925 15.1037 10.1062 15.1036H8.85799C8.73835 15.1036 8.67228 15.2411 8.74549 15.3339L13.6366 21.5304C13.8651 21.8196 14.3044 21.8196 14.5348 21.5304L23.2562 10.4786C23.3294 10.3875 23.2633 10.25 23.1437 10.25Z" fill="#2E2EE5"/></svg>';
            } else if (index === stepIndex) {
              // Current step - blue with number
              stepElement.classList.add('bg-[#2E2EE5]', 'text-white');
              stepElement.innerHTML = (index + 1).toString();
            } else {
              // Future steps - transparent with border
              stepElement.classList.add('bg-transparent', 'border-2', 'border-[#B3B3B3]', 'text-[#B3B3B3]');
              stepElement.innerHTML = (index + 1).toString();
            }
          }
        });

        const titleElements = document.querySelectorAll(`[id="demo-step-title-${s.id}"]`);
        const descriptionElements = document.querySelectorAll(`[id="demo-step-description-${s.id}"]`);

        titleElements.forEach((titleElement) => {
          if (titleElement instanceof HTMLElement) {
            // Remove all possible classes
            titleElement.classList.remove('text-primary_text', 'text-secondary_text');

            if (index < stepIndex) {
              // Completed steps - primary text
              titleElement.classList.add('text-primary_text');
            } else if (index === stepIndex) {
              // Current step - primary text
              titleElement.classList.add('text-primary_text');
            } else {
              // Future steps - secondary text
              titleElement.classList.add('text-secondary_text');
            }
          }
        });
        descriptionElements.forEach((descriptionElement) => {
          if (descriptionElement instanceof HTMLElement) {
            // Remove all possible classes
            descriptionElement.classList.remove('text-primary_text', 'text-secondary_text');

            if (index < stepIndex) {
              // Completed steps - primary text
              descriptionElement.classList.add('text-primary_text');
            } else if (index === stepIndex) {
              // Current step - primary text
              descriptionElement.classList.add('text-primary_text');
            } else {
              // Future steps - secondary text
              descriptionElement.classList.add('text-secondary_text');
            }
          }
        });
      });

      // Update connecting lines
      updateConnectingLines(currentStepIndex);
    };

    const updateConnectingLines = (currentStepIndex: number) => {
      // Update horizontal connecting lines (desktop) based on current step
      const lineElements = document.querySelectorAll('[data-connecting-line]');
      lineElements.forEach((lineElement, index) => {
        if (lineElement instanceof HTMLElement) {
          if (index < currentStepIndex) {
            // Completed lines - blue
            lineElement.classList.remove('bg-[#E5E5E9]');
            lineElement.classList.add('bg-[#2E2EE5]');
          } else {
            // Future lines - gray
            lineElement.classList.remove('bg-[#2E2EE5]');
            lineElement.classList.add('bg-[#E5E5E9]');
          }
        }
      });

      // Update vertical connecting lines (mobile) based on current step
      const verticalLineElements = document.querySelectorAll('[data-connecting-line-vertical]');
      verticalLineElements.forEach((lineElement, index) => {
        if (lineElement instanceof HTMLElement) {
          if (index < currentStepIndex) {
            // Completed lines - blue
            lineElement.classList.remove('bg-[#E5E5E9]');
            lineElement.classList.add('bg-[#2E2EE5]');
          } else {
            // Future lines - gray
            lineElement.classList.remove('bg-[#2E2EE5]');
            lineElement.classList.add('bg-[#E5E5E9]');
          }
        }
      });
    };

    const playVideo = (stepIndex: number) => {
      // Hide all videos first
      demoSteps.forEach((s, index) => {
        const video = document.getElementById(`demo-video-${index}`) as HTMLVideoElement;
        if (video) {
          video.pause();
          video.className = video.className.replace('opacity-100', 'opacity-0');
        }
      });

      // Show and play the target video
      const targetVideo = document.getElementById(`demo-video-${stepIndex}`) as HTMLVideoElement;
      if (targetVideo) {
        targetVideo.className = targetVideo.className.replace('opacity-0', 'opacity-100');
        updateStepUI(currentStepIndex);

        // Progressive loading: Load video if not already loaded
        if (targetVideo.readyState < 3) {
          // Load video data first
          targetVideo.load();

          const loadingHandler = () => {
            targetVideo.currentTime = 0;
            targetVideo.play().catch((error) => {
              console.log('Video autoplay failed:', error);
            });
            targetVideo.removeEventListener('canplay', loadingHandler);
          };

          targetVideo.addEventListener('canplay', loadingHandler);
        } else {
          // Video already loaded, play immediately
          targetVideo.currentTime = 0;
          targetVideo.play().catch((error) => {
            console.log('Video autoplay failed:', error);
          });
        }

        // Preload next video for smooth experience
        preloadNextVideo(currentStepIndex);
      }
    };

    const preloadNextVideo = (currentIndex: number) => {
      const nextIndex = (currentIndex + 1) % demoSteps.length;
      const nextVideo = document.getElementById(`demo-video-${nextIndex}`) as HTMLVideoElement;

      if (nextVideo && nextVideo.readyState < 2) {
        nextVideo.load();
      }
    };

    const setupVideoEvents = () => {
      // Set up event listeners for all videos
      demoSteps.forEach((s, index) => {
        const video = document.getElementById(`demo-video-${index}`) as HTMLVideoElement;
        if (video) {
          video.addEventListener('ended', () => {
            // Only advance if this is the currently visible video
            if (video.className.includes('opacity-100')) {
              currentStepIndex = (currentStepIndex + 1) % demoSteps.length;

              setTimeout(() => {
                playVideo(currentStepIndex);
              }, 500);
            }
          });

          video.muted = true;
          video.playsInline = true;
        }
      });
    };

    const startDemoWhenVisible = () => {
      // Find the demo video container
      const demoContainer = document.querySelector('.video-demo-container');

      if (!demoContainer || hasStarted) return;

      const observer = new IntersectionObserver(
        (entries) => {
          entries.forEach((entry) => {
            if (entry.isIntersecting && !hasStarted) {
              hasStarted = true;
              // Start playing the first video when component comes into view
              setTimeout(() => {
                playVideo(currentStepIndex);
              }, 0); // Small delay to ensure smooth entrance

              // Disconnect observer after first trigger
              observer.disconnect();
            }
          });
        },
        {
          root: null,
          rootMargin: '0px 0px 0px 0px',
          threshold: 0.1,
        },
      );

      observer.observe(demoContainer);
    };

    demoSteps.forEach((step, index) => {
      const allStepElements = document.querySelectorAll(`[id="demo-step-${step.id}"]`);

      allStepElements.forEach((stepElement) => {
        if (stepElement instanceof HTMLElement) {
          stepElement.style.cursor = 'pointer';
          stepElement.addEventListener('click', (e) => {
            e.preventDefault();
            e.stopPropagation();
            currentStepIndex = index;
            playVideo(currentStepIndex);
          });
        }
      });
    });

    setupVideoEvents();
    startDemoWhenVisible();

    return () => {
      // Clean up event listeners for all videos
      demoSteps.forEach((s, index) => {
        const video = document.getElementById(`demo-video-${index}`) as HTMLVideoElement;
        if (video) {
          video.removeEventListener('ended', () => {});
        }
      });
    };
  }, [demoSteps]);

  return null;
};
