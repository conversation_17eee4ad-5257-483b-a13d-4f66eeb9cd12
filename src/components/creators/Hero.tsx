'use client';

import { routePaths } from '@/config/constant';
import UserInfoContext from '@/utils/providers/UserInfoProvider';
import Image from 'next/image';
import Link from 'next/link';
import React, { useContext } from 'react';

interface HeroProps {
  className?: string;
}

export const Hero: React.FC<HeroProps> = ({ className = '' }) => {
  const { isLoggedIn } = useContext(UserInfoContext);

  return (
    <section className={`relative overflow-hidden bg-white ${className}`}>
      <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-16">
        <div className="relative min-h-[400px] py-12 sm:py-16 lg:py-20">
          <div className="hidden lg:block">
            <div className="relative h-[510px] w-full">
              <div className="absolute left-0 top-0 z-10">
                <div className="h-[128px] w-[90vw] min-w-64 max-w-[50rem] rounded-2xl bg-primary-500 px-8 py-4 text-white">
                  <div className="flex h-full w-full items-center justify-center">
                    <h2 className="whitespace-nowrap text-display-xl">Giáo dục sáng tạo</h2>
                  </div>
                </div>
              </div>

              <div className="absolute right-0 top-0 z-10">
                <div className="relative">
                  <Image
                    width={295}
                    height={325}
                    src="/images/hero-block-1.png"
                    alt="Hero Block 1"
                    className="object-contain"
                  />
                  <div className="absolute bottom-0 right-[45px] flex items-center justify-center">
                    <Image
                      width={243}
                      height={256}
                      src="/images/hero-teacher.png"
                      alt="Hero Teacher"
                      className="object-contain"
                      quality={95}
                      sizes="243px"
                    />
                  </div>
                </div>
              </div>

              <div className="absolute left-0 top-36 z-10">
                <div className="relative">
                  <Image
                    width={495}
                    height={282}
                    src="/images/hero-block-3.png"
                    alt="Hero Block 3 - Purple"
                    className="object-contain"
                  />
                  <div className="absolute bottom-0 flex items-center justify-start pl-4">
                    <Image
                      width={310}
                      height={257}
                      src="/images/hero-students.png"
                      alt="Students/Persons"
                      className="object-contain"
                      quality={95}
                      sizes="310px"
                    />
                  </div>
                  <div className="absolute bottom-0 right-4">
                    <Image
                      width={170}
                      height={101}
                      src="/images/hero-block-2.png"
                      alt="Yellow Element"
                      className="object-contain"
                    />
                  </div>
                </div>
              </div>

              <div className="absolute left-1/2 top-36 z-10 -translate-x-1/2 transform">
                <h1 className="text-center text-display-xl text-primary_text">theo cách</h1>
              </div>

              <div className="absolute right-0 top-[18.35rem] z-10 h-[8.25rem] w-[39rem]">
                <div className="h-full rounded-2xl bg-orange-500 px-8 py-4 text-white">
                  <div className="flex h-full w-full items-center justify-center">
                    <h2 className="whitespace-nowrap text-display-xl">của riêng bạn</h2>
                  </div>
                </div>
              </div>

              <div className="absolute bottom-0 left-0 top-[28rem] z-10 h-[92px] w-[44rem]">
                <div className="h-full w-full justify-center rounded-3xl bg-yellow-400 pb-4 pl-7 pr-7 pt-[17px]">
                  <p className="font-weight-600 text-[22px] font-semibold leading-[28px] text-orange-600">
                    Trở thành Edu-Creator - Biến tri thức thành khóa học, lan tỏa giá trị và kiến tạo di sản giáo dục bền vững.
                  </p>
                </div>
              </div>

              <div className="group absolute bottom-0 right-0 top-[28rem] z-10 cursor-pointer">
                <Link href={isLoggedIn ? routePaths.profile.children.creator.path : routePaths.login}>
                  <div className="relative">
                    <svg
                      width="421"
                      height="92"
                      viewBox="0 0 421 92"
                      fill="none"
                      xmlns="http://www.w3.org/2000/svg"
                      className="transition-all duration-300"
                    >
                      <path
                        d="M375 0C400.405 0 421 20.5949 421 46C421 71.4051 400.405 92 375 92H151.5C134.633 92 119.888 82.922 111.882 69.3857C109.063 64.6195 104.288 61 98.75 61C93.2124 61 88.4374 64.6195 85.6182 69.3857C77.6116 82.922 62.8665 92 46 92C20.5949 92 2.06163e-06 71.4051 0 46C0 20.5949 20.5949 0 46 0C62.8665 0 77.6116 9.07797 85.6182 22.6143C88.4374 27.3805 93.2124 31 98.75 31C104.288 31 109.063 27.3805 111.882 22.6143C119.888 9.07797 134.633 0 151.5 0H375Z"
                        className="button-background transition-colors group-hover:fill-secondary-400"
                      />
                      <path
                        d="M154.16 48.88C154.16 47.68 153.3 46.96 151.94 46.96H149.18V50.72H152C153.36 50.72 154.16 50.06 154.16 48.88ZM153.82 43.06C153.82 41.94 153.06 41.3 151.74 41.3H149.18V44.8H151.74C153.06 44.8 153.82 44.2 153.82 43.06ZM157 49.22C157 51.44 155.3 53 152.4 53H146.38V39.04H152.14C155.06 39.04 156.68 40.56 156.68 42.64C156.68 44.34 155.66 45.4 154.3 45.84C155.88 46.12 157 47.58 157 49.22ZM166.501 30.78L167.761 32.5L164.381 34.78L163.201 33.32L166.501 30.78ZM168.321 53L167.401 50.34H161.841L160.921 53H157.981L163.001 39.02H166.261L171.281 53H168.321ZM166.641 48.1L164.621 42.26L162.601 48.1H166.641ZM164.641 38.16C162.741 38.16 161.221 37.12 161.221 35.18H163.381C163.381 35.94 163.861 36.32 164.641 36.32C165.421 36.32 165.901 35.94 165.901 35.18H168.061C168.061 37.12 166.541 38.16 164.641 38.16ZM171.011 41.3V39.04H181.251V41.3H177.531V53H174.731V41.3H171.011ZM193.12 39.04C197.58 39.04 200.48 41.8 200.48 46.04C200.48 50.26 197.58 53 193.12 53H188.22V47.14H186.82V44.94H188.22V39.04H193.12ZM191.04 50.72H193.02C195.96 50.72 197.6 48.94 197.6 46.04C197.6 43.14 195.96 41.38 193.02 41.38H191.04V44.94H194.38V47.14H191.04V50.72ZM208.011 33.38L209.271 31.66L212.571 34.2L211.391 35.66L208.011 33.38ZM211.251 53L210.331 50.34H204.771L203.851 53H200.911L205.931 39.02H209.191L214.211 53H211.251ZM209.571 48.1L207.551 42.26L205.531 48.1H209.571ZM207.551 36.7L205.231 38.16L204.131 36.62L207.551 34.4L210.971 36.62L209.891 38.16L207.551 36.7ZM216.047 47.66V39.04H218.847V47.68C218.847 49.6 219.887 50.6 221.667 50.6C223.467 50.6 224.507 49.6 224.507 47.68V39.04H227.327V47.66C227.327 51.34 224.687 53.14 221.627 53.14C218.567 53.14 216.047 51.34 216.047 47.66ZM234.058 41.3V39.04H244.298V41.3H240.578V53H237.778V41.3H234.058ZM250.715 57.92C249.755 57.92 249.055 57.22 249.055 56.34C249.055 55.44 249.755 54.74 250.715 54.74C251.675 54.74 252.395 55.44 252.395 56.34C252.395 57.22 251.675 57.92 250.715 57.92ZM254.415 53L253.495 50.34H247.935L247.015 53H244.075L249.095 39.02H252.355L257.375 53H254.415ZM252.735 48.1L250.715 42.26L248.695 48.1H252.735ZM272.07 45.98C272.07 50.18 268.89 53.14 264.93 53.14C260.99 53.14 257.77 50.18 257.77 45.98C257.77 41.8 260.99 38.84 264.93 38.84C268.91 38.84 272.07 41.8 272.07 45.98ZM260.65 45.98C260.65 48.82 262.39 50.64 264.93 50.64C267.45 50.64 269.19 48.82 269.19 45.98C269.19 43.14 267.45 41.36 264.93 41.36C262.39 41.36 260.65 43.14 260.65 45.98ZM286.799 53L281.719 46.78V53H278.919V39.04H281.719V45.3L286.799 39.04H290.179L284.419 45.96L290.339 53H286.799ZM300.961 53V47.06H294.981V53H292.181V39.04H294.981V44.78H300.961V39.04H303.761V53H300.961ZM320.137 45.98C320.137 50.18 316.957 53.14 312.997 53.14C309.057 53.14 305.837 50.18 305.837 45.98C305.837 41.8 309.057 38.84 312.997 38.84C316.977 38.84 320.137 41.8 320.137 45.98ZM308.717 45.98C308.717 48.82 310.457 50.64 312.997 50.64C315.517 50.64 317.257 48.82 317.257 45.98C317.257 43.14 315.517 41.36 312.997 41.36C310.457 41.36 308.717 43.14 308.717 45.98ZM330.899 53L329.979 50.34H324.419L323.499 53H320.559L325.579 39.02H328.839L333.859 53H330.899ZM329.219 48.1L327.199 42.26L325.179 48.1H329.219ZM329.059 34.02L330.179 35.9L325.939 37.96L324.919 36.32L329.059 34.02ZM349.281 53V47.06H343.301V53H340.501V39.04H343.301V44.78H349.281V39.04H352.081V53H349.281ZM361.297 57.92C360.337 57.92 359.637 57.22 359.637 56.34C359.637 55.44 360.337 54.74 361.297 54.74C362.257 54.74 362.977 55.44 362.977 56.34C362.977 57.22 362.257 57.92 361.297 57.92ZM368.457 45.98C368.457 50.18 365.277 53.14 361.317 53.14C357.377 53.14 354.157 50.18 354.157 45.98C354.157 41.8 357.377 38.84 361.317 38.84C365.297 38.84 368.457 41.8 368.457 45.98ZM357.037 45.98C357.037 48.82 358.777 50.64 361.317 50.64C363.837 50.64 365.577 48.82 365.577 45.98C365.577 43.14 363.837 41.36 361.317 41.36C358.777 41.36 357.037 43.14 357.037 45.98ZM369.86 46C369.86 41.82 372.92 38.86 376.94 38.86C380.04 38.86 382.56 40.52 383.5 43.38H380.28C379.62 42.04 378.42 41.38 376.92 41.38C374.48 41.38 372.74 43.16 372.74 46C372.74 48.82 374.48 50.62 376.92 50.62C378.42 50.62 379.62 49.96 380.28 48.6H383.5C382.56 51.48 380.04 53.12 376.94 53.12C372.92 53.12 369.86 50.18 369.86 46Z"
                        fill="white"
                        className="button-text transition-colors duration-300 group-hover:fill-primary-700"
                      />
                    </svg>

                    <div className="absolute inset-0 ml-7 flex items-center justify-start">
                      <svg
                        width="35"
                        height="36"
                        viewBox="0 0 35 36"
                        fill="none"
                        xmlns="http://www.w3.org/2000/svg"
                        className="transition-colors duration-300"
                        style={{
                          transformOrigin: 'center',
                          animation: 'arrowRotate 7.5s infinite linear',
                        }}
                      >
                        <path
                          fillRule="evenodd"
                          clipRule="evenodd"
                          d="M0.748047 18C0.748047 16.5798 1.86234 15.4286 3.2369 15.4286H26.2506L15.5662 4.3897C14.5942 3.3855 14.5942 1.75736 15.5662 0.753155C16.5382 -0.251053 18.114 -0.251053 19.086 0.753155L34.0191 16.1817C34.991 17.1859 34.991 18.8141 34.0191 19.8183L19.086 35.2468C18.114 36.2511 16.5382 36.2511 15.5662 35.2468C14.5942 34.2426 14.5942 32.6145 15.5662 31.6103L26.2506 20.5714H3.2369C1.86234 20.5714 0.748047 19.4202 0.748047 18Z"
                          fill="white"
                          className="transition-colors duration-300 group-hover:fill-primary-700"
                          style={{
                            animation: 'changeArrowColor 2.5s infinite steps(1, end)',
                          }}
                        />
                      </svg>
                    </div>
                  </div>
                </Link>
              </div>
            </div>
          </div>

          <div className="lg:hidden">
            <div className="flex flex-col">
              <div className="flex w-full items-start justify-between gap-3 sm:gap-4 lg:gap-6">
                <div className="max-w-[60%] flex-1 lg:max-w-[55%]">
                  <div className="min-h-[116px] w-full min-w-[171px] rounded-2xl bg-primary-500 px-4 py-3 text-white sm:px-6 sm:py-4 lg:min-h-[140px] lg:px-8 lg:py-6">
                    <div className="flex h-full w-full items-center justify-center">
                      <h2 className="xl:text-2xl text-center text-3xl font-medium leading-snug sm:text-base md:text-lg lg:text-xl">
                        Giáo dục <br />
                        sáng tạo
                      </h2>
                    </div>
                  </div>
                </div>

                <div className="flex flex-shrink-0 items-center justify-center">
                  <div className="relative h-[136px] min-h-[136px] w-[40vw] min-w-[150px] max-w-[200px] lg:h-[180px] lg:min-h-[180px] lg:max-w-[250px]">
                    <Image
                      fill
                      src="/images/hero-block-1.png"
                      alt="Hero Block 1"
                      className="object-contain"
                      sizes="(max-width: 480px) 150px, (max-width: 768px) 180px, (max-width: 1024px) 220px, 250px"
                    />
                    <div className="absolute bottom-0 right-[20%] flex items-center justify-center">
                      <div className="relative h-[128px] min-h-[128px] w-[30vw] min-w-[122px] max-w-[150px] lg:h-[160px] lg:min-h-[160px] lg:max-w-[180px]">
                        <Image
                          fill
                          src="/images/hero-teacher.png"
                          alt="Hero Teacher"
                          className="object-contain"
                          quality={95}
                          sizes="(max-width: 480px) 122px, (max-width: 768px) 135px, (max-width: 1024px) 160px, 180px"
                        />
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <div className="relative flex w-full items-center justify-between gap-3 sm:gap-4 lg:gap-6">
                <div className="max-w-[65%] flex-1 lg:max-w-[60%]">
                  <div className="relative min-h-[143px] w-full min-w-[251px] lg:min-h-[180px]">
                    <Image
                      fill
                      src="/images/hero-block-3.png"
                      alt="Hero Block 3 - Purple"
                      className="relative object-contain"
                      sizes="(max-width: 480px) 251px, (max-width: 768px) 300px, (max-width: 1024px) 400px, 450px"
                    />
                    <div className="absolute bottom-0 flex items-center justify-start pl-2 sm:pl-4 lg:pl-6">
                      <div className="relative h-[130px] min-h-[130px] w-[20vw] min-w-[157px] max-w-[180px] lg:h-[160px] lg:min-h-[160px] lg:max-w-[220px]">
                        <Image
                          fill
                          src="/images/hero-students.png"
                          alt="Students/Persons"
                          className="relative object-contain"
                          quality={95}
                          sizes="(max-width: 480px) 157px, (max-width: 768px) 170px, (max-width: 1024px) 200px, 220px"
                        />
                      </div>
                    </div>
                    <div className="absolute bottom-0 right-2 sm:right-4 lg:right-6">
                      <div className="relative h-[43px] min-h-[43px] w-[10vw] min-w-[85px] max-w-[100px] lg:h-[55px] lg:min-h-[55px] lg:max-w-[120px]">
                        <Image
                          fill
                          src="/images/hero-block-2.png"
                          alt="Yellow Element"
                          className="object-contain"
                          sizes="(max-width: 480px) 85px, (max-width: 768px) 90px, (max-width: 1024px) 110px, 120px"
                        />
                      </div>
                    </div>
                  </div>
                </div>

                <div className="absolute bottom-20 right-[1rem] flex-shrink-0 rounded-xl px-2 py-1 sm:px-3 sm:py-2 lg:px-4 lg:py-3">
                  <h1 className="text-center text-[32px] font-medium tracking-tight text-primary_text">theo cách</h1>
                </div>
                <div className="absolute bottom-0 right-0 flex min-h-[67px] min-w-[82px] items-center rounded-xl bg-orange-500 lg:min-h-[180px]">
                  <h2 className="w-full text-center text-[32px] font-medium text-white">của</h2>
                </div>
              </div>

              <div className="relative mt-[1rem] flex w-full items-center justify-center">
                <div className="flex-1">
                  <div className="relative min-h-[144px] w-full min-w-[230px] flex-1 lg:min-h-[180px]">
                    <Image
                      fill
                      src="/images/hero-m-block-5.svg"
                      alt="Hero Block Yellow"
                      className="relative object-contain"
                      sizes="(max-width: 480px) 343px, (max-width: 768px) 380px, (max-width: 1024px) 450px, 550px"
                    />
                  </div>
                </div>
              </div>

              <div className="group mt-[1rem] max-w-sm cursor-pointer">
                <div className="relative">
                  <Link href={isLoggedIn ? routePaths.profile.children.creator.path : routePaths.login}>
                    <svg
                      width="100%"
                      height="75"
                      viewBox="0 0 421 92"
                      fill="none"
                      xmlns="http://www.w3.org/2000/svg"
                      className="transition-all duration-300"
                      preserveAspectRatio="none"
                    >
                      <path
                        d="M375 0C400.405 0 421 20.5949 421 46C421 71.4051 400.405 92 375 92H151.5C134.633 92 119.888 82.922 111.882 69.3857C109.063 64.6195 104.288 61 98.75 61C93.2124 61 88.4374 64.6195 85.6182 69.3857C77.6116 82.922 62.8665 92 46 92C20.5949 92 2.06163e-06 71.4051 0 46C0 20.5949 20.5949 0 46 0C62.8665 0 77.6116 9.07797 85.6182 22.6143C88.4374 27.3805 93.2124 31 98.75 31C104.288 31 109.063 27.3805 111.882 22.6143C119.888 9.07797 134.633 0 151.5 0H375Z"
                        className="button-background transition-colors duration-300 group-hover:fill-secondary-400"
                      />
                      <path
                        d="M154.16 48.88C154.16 47.68 153.3 46.96 151.94 46.96H149.18V50.72H152C153.36 50.72 154.16 50.06 154.16 48.88ZM153.82 43.06C153.82 41.94 153.06 41.3 151.74 41.3H149.18V44.8H151.74C153.06 44.8 153.82 44.2 153.82 43.06ZM157 49.22C157 51.44 155.3 53 152.4 53H146.38V39.04H152.14C155.06 39.04 156.68 40.56 156.68 42.64C156.68 44.34 155.66 45.4 154.3 45.84C155.88 46.12 157 47.58 157 49.22ZM166.501 30.78L167.761 32.5L164.381 34.78L163.201 33.32L166.501 30.78ZM168.321 53L167.401 50.34H161.841L160.921 53H157.981L163.001 39.02H166.261L171.281 53H168.321ZM166.641 48.1L164.621 42.26L162.601 48.1H166.641ZM164.641 38.16C162.741 38.16 161.221 37.12 161.221 35.18H163.381C163.381 35.94 163.861 36.32 164.641 36.32C165.421 36.32 165.901 35.94 165.901 35.18H168.061C168.061 37.12 166.541 38.16 164.641 38.16ZM171.011 41.3V39.04H181.251V41.3H177.531V53H174.731V41.3H171.011ZM193.12 39.04C197.58 39.04 200.48 41.8 200.48 46.04C200.48 50.26 197.58 53 193.12 53H188.22V47.14H186.82V44.94H188.22V39.04H193.12ZM191.04 50.72H193.02C195.96 50.72 197.6 48.94 197.6 46.04C197.6 43.14 195.96 41.38 193.02 41.38H191.04V44.94H194.38V47.14H191.04V50.72ZM208.011 33.38L209.271 31.66L212.571 34.2L211.391 35.66L208.011 33.38ZM211.251 53L210.331 50.34H204.771L203.851 53H200.911L205.931 39.02H209.191L214.211 53H211.251ZM209.571 48.1L207.551 42.26L205.531 48.1H209.571ZM207.551 36.7L205.231 38.16L204.131 36.62L207.551 34.4L210.971 36.62L209.891 38.16L207.551 36.7ZM216.047 47.66V39.04H218.847V47.68C218.847 49.6 219.887 50.6 221.667 50.6C223.467 50.6 224.507 49.6 224.507 47.68V39.04H227.327V47.66C227.327 51.34 224.687 53.14 221.627 53.14C218.567 53.14 216.047 51.34 216.047 47.66ZM234.058 41.3V39.04H244.298V41.3H240.578V53H237.778V41.3H234.058ZM250.715 57.92C249.755 57.92 249.055 57.22 249.055 56.34C249.055 55.44 249.755 54.74 250.715 54.74C251.675 54.74 252.395 55.44 252.395 56.34C252.395 57.22 251.675 57.92 250.715 57.92ZM254.415 53L253.495 50.34H247.935L247.015 53H244.075L249.095 39.02H252.355L257.375 53H254.415ZM252.735 48.1L250.715 42.26L248.695 48.1H252.735ZM272.07 45.98C272.07 50.18 268.89 53.14 264.93 53.14C260.99 53.14 257.77 50.18 257.77 45.98C257.77 41.8 260.99 38.84 264.93 38.84C268.91 38.84 272.07 41.8 272.07 45.98ZM260.65 45.98C260.65 48.82 262.39 50.64 264.93 50.64C267.45 50.64 269.19 48.82 269.19 45.98C269.19 43.14 267.45 41.36 264.93 41.36C262.39 41.36 260.65 43.14 260.65 45.98ZM286.799 53L281.719 46.78V53H278.919V39.04H281.719V45.3L286.799 39.04H290.179L284.419 45.96L290.339 53H286.799ZM300.961 53V47.06H294.981V53H292.181V39.04H294.981V44.78H300.961V39.04H303.761V53H300.961ZM320.137 45.98C320.137 50.18 316.957 53.14 312.997 53.14C309.057 53.14 305.837 50.18 305.837 45.98C305.837 41.8 309.057 38.84 312.997 38.84C316.977 38.84 320.137 41.8 320.137 45.98ZM308.717 45.98C308.717 48.82 310.457 50.64 312.997 50.64C315.517 50.64 317.257 48.82 317.257 45.98C317.257 43.14 315.517 41.36 312.997 41.36C310.457 41.36 308.717 43.14 308.717 45.98ZM330.899 53L329.979 50.34H324.419L323.499 53H320.559L325.579 39.02H328.839L333.859 53H330.899ZM329.219 48.1L327.199 42.26L325.179 48.1H329.219ZM329.059 34.02L330.179 35.9L325.939 37.96L324.919 36.32L329.059 34.02ZM349.281 53V47.06H343.301V53H340.501V39.04H343.301V44.78H349.281V39.04H352.081V53H349.281ZM361.297 57.92C360.337 57.92 359.637 57.22 359.637 56.34C359.637 55.44 360.337 54.74 361.297 54.74C362.257 54.74 362.977 55.44 362.977 56.34C362.977 57.22 362.257 57.92 361.297 57.92ZM368.457 45.98C368.457 50.18 365.277 53.14 361.317 53.14C357.377 53.14 354.157 50.18 354.157 45.98C354.157 41.8 357.377 38.84 361.317 38.84C365.297 38.84 368.457 41.8 368.457 45.98ZM357.037 45.98C357.037 48.82 358.777 50.64 361.317 50.64C363.837 50.64 365.577 48.82 365.577 45.98C365.577 43.14 363.837 41.36 361.317 41.36C358.777 41.36 357.037 43.14 357.037 45.98ZM369.86 46C369.86 41.82 372.92 38.86 376.94 38.86C380.04 38.86 382.56 40.52 383.5 43.38H380.28C379.62 42.04 378.42 41.38 376.92 41.38C374.48 41.38 372.74 43.16 372.74 46C372.74 48.82 374.48 50.62 376.92 50.62C378.42 50.62 379.62 49.96 380.28 48.6H383.5C382.56 51.48 380.04 53.12 376.94 53.12C372.92 53.12 369.86 50.18 369.86 46Z"
                        fill="white"
                        className="button-text transition-colors duration-300 group-hover:fill-primary-700"
                      />
                    </svg>

                    <div className="absolute inset-0 ml-5 flex items-center justify-start">
                      <svg
                        width="31"
                        height="29"
                        viewBox="0 0 35 36"
                        fill="none"
                        xmlns="http://www.w3.org/2000/svg"
                        className="transition-colors duration-300"
                        style={{
                          transformOrigin: 'center',
                          animation: 'arrowRotate 7.5s infinite linear',
                        }}
                      >
                        <path
                          fillRule="evenodd"
                          clipRule="evenodd"
                          d="M0.748047 18C0.748047 16.5798 1.86234 15.4286 3.2369 15.4286H26.2506L15.5662 4.3897C14.5942 3.3855 14.5942 1.75736 15.5662 0.753155C16.5382 -0.251053 18.114 -0.251053 19.086 0.753155L34.0191 16.1817C34.991 17.1859 34.991 18.8141 34.0191 19.8183L19.086 35.2468C18.114 36.2511 16.5382 36.2511 15.5662 35.2468C14.5942 34.2426 14.5942 32.6145 15.5662 31.6103L26.2506 20.5714H3.2369C1.86234 20.5714 0.748047 19.4202 0.748047 18Z"
                          fill="white"
                          className="transition-colors duration-300 group-hover:fill-primary-700"
                          style={{
                            animation: 'changeArrowColor 2.5s infinite steps(1, end)',
                          }}
                        />
                      </svg>
                    </div>
                  </Link>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <style>{`
        @keyframes arrowRotate {
          0%,
          33.33% {
            transform: rotate(-45deg);
          }
          33.34%,
          66.66% {
            transform: rotate(0deg);
          }
          66.67%,
          100% {
            transform: rotate(45deg);
          }
        }
        @keyframes changeFillColor {
          0% {
            fill: #2e2ee5;
          }
          50% {
            fill: #50e5be;
          }
          100% {
            fill: #2e2ee5;
          }
        }
        @keyframes changeTextColor {
          0% {
            fill: #ffffff;
          }
          50% {
            fill: #1b1bb2;
          }
          100% {
            fill: #ffffff;
          }
        }
        @keyframes changeArrowColor {
          0% {
            fill: #ffffff;
          }
          50% {
            fill: #1b1bb2;
          }
          100% {
            fill: #ffffff;
          }
        }
        .button-background {
          animation: changeFillColor 2.5s infinite steps(1, end);
        }
        .button-text {
          animation: changeTextColor 2.5s infinite steps(1, end);
        }
      `}</style>
    </section>
  );
};

export default Hero;
