'use client';

import { useEffect } from 'react';

interface ONEInteractiveProps {
  images: Array<{
    id: string;
    src: string;
    alt: string;
    width: number;
    height: number;
    type: 'image' | 'video';
  }>;
}

export const ONEInteractive: React.FC<ONEInteractiveProps> = ({ images }) => {
  useEffect(() => {
    const slider = document.getElementById('one-slider') as HTMLElement;
    if (!slider) return;

    let translateX = 0;
    let animationId: number;
    let isPaused = false;

    // Calculate total width of all images including gaps (32px gap between images)
    const gapSize = 32;
    const totalOriginalWidth = images.reduce((sum, image) => sum + image.width + gapSize, 0);

    // Enable hardware acceleration for smoother performance
    slider.style.willChange = 'transform';
    slider.style.backfaceVisibility = 'hidden';
    slider.style.perspective = '1000px';

    const autoScroll = () => {
      if (!isPaused) {
        translateX -= 2.5;

        if (Math.abs(translateX) >= totalOriginalWidth) {
          translateX = 0;
        }

        slider.style.transform = `translate3d(${translateX}px, 0, 0)`;
      }
      animationId = requestAnimationFrame(autoScroll);
    };

    // Start auto-scroll
    autoScroll();

    // Pause on hover
    const handleMouseEnter = () => {
      isPaused = true;
    };

    const handleMouseLeave = () => {
      isPaused = false;
    };

    slider.addEventListener('mouseenter', handleMouseEnter);
    slider.addEventListener('mouseleave', handleMouseLeave);

    // Pause on touch (mobile)
    const handleTouchStart = () => {
      isPaused = true;
    };

    const handleTouchEnd = () => {
      setTimeout(() => {
        isPaused = false;
      }, 2000);
    };

    slider.addEventListener('touchstart', handleTouchStart);
    slider.addEventListener('touchend', handleTouchEnd);

    return () => {
      if (animationId) {
        cancelAnimationFrame(animationId);
      }
      slider.removeEventListener('mouseenter', handleMouseEnter);
      slider.removeEventListener('mouseleave', handleMouseLeave);
      slider.removeEventListener('touchstart', handleTouchStart);
      slider.removeEventListener('touchend', handleTouchEnd);
    };
  }, [images]);

  return null;
};
