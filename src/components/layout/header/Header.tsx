'use client';

import { Icon } from '@/components/client';
import HeaderSearch from '@/components/header/HeaderSearch';
import { Breadcrumb, Button, Typography } from '@/components/ui';
import { routePaths } from '@/config';
import { useCourseDetail } from '@/modules/courses/hooks';
import { UserInfo } from '@/type';
import { useUserInfoProvider } from '@/utils/providers/UserInfoProvider';
import { redirectToBillingCycle } from '@/utils/url.util';
import { BellIcon, QuestionMarkCircleIcon } from '@heroicons/react/24/outline';
import { Avatar } from 'antd';
import Image from 'next/image';
import { useParams, usePathname, useRouter } from 'next/navigation';

const getAvatar = (userInfo: UserInfo | undefined) => {
  if (!userInfo) {
    return null;
  }

  const avatar = userInfo?.avatar || '/images/avatar.png';

  return (
    <div className={'size-full bg-neutral-50'}>
      <Image src={avatar} width={46} height={46} className={'object-cover'} alt={'avatar'} />
    </div>
  );
};

interface PathMapping {
  name: string;
  href: string;
}

const breadcrumbPathMapping: Record<string, PathMapping> = {
  [routePaths.profile.path]: {
    name: 'Việc học của tôi',
    href: routePaths.profile.path,
  },
  [routePaths.profile.children.course.children.discovery.path]: {
    name: 'Khám phá khóa học',
    href: routePaths.profile.children.course.children.discovery.path,
  },
  [routePaths.profile.children.course.children.myCourse.path]: {
    name: 'Khóa học của tôi',
    href: routePaths.profile.children.course.children.myCourse.path,
  },
  [routePaths.profile.children.course.children.wishlist.path]: {
    name: 'Wish list',
    href: routePaths.profile.children.course.children.wishlist.path,
  },
  [routePaths.profile.children.creator.path]: {
    name: 'Thư viện sáng tạo',
    href: routePaths.profile.children.creator.path,
  },
  [routePaths.profile.children.setting.path]: {
    name: 'Cài đặt tài khoản',
    href: routePaths.profile.children.setting.path,
  },
};

const getCurrentBreadcrumb = (path: string): PathMapping => {
  return breadcrumbPathMapping[path] || breadcrumbPathMapping[routePaths.profile.path];
};

type HeaderProps = {
  onClickAvatarAction: () => void;
};

export default function Header({ onClickAvatarAction }: HeaderProps) {
  const { userInfo, isLoggedIn, hasSubscription } = useUserInfoProvider();
  const pathname = usePathname();
  const router = useRouter();

  const handleUpgradeClick = () => {
    if (!isLoggedIn || !hasSubscription) {
      redirectToBillingCycle(router);
    } else {
      router.push('/profile/setting');
    }
  };

  const params = useParams<{ id: string }>();

  const isCourseDetail = pathname.startsWith(
    routePaths.profile.children.course.children.detail.path.replace(':id', params.id || ''),
  );

  const { courseDetailData } = useCourseDetail({ courseId: isCourseDetail ? params?.id : '' });

  return (
    <div className="flex items-center justify-between border-b border-neutral-200 px-8 py-6">
      <Breadcrumb items={[{ title: courseDetailData?.topic?.topicName }, { title: courseDetailData?.courseName }]} />

      <div className="flex items-center gap-3">
        <HeaderSearch width={230} />

        {!hasSubscription && (
          <Button variant="secondary" onClick={handleUpgradeClick}>
            <Typography variant="labelMd" className="text-primary">
              Mở khóa toàn bộ nội dung
            </Typography>
          </Button>
        )}

        <div className="flex items-center rounded-full bg-neutral-50 p-2">
          <Icon icon={<QuestionMarkCircleIcon />} />
        </div>

        <div className="flex items-center rounded-full bg-neutral-50 p-2">
          <Icon icon={<BellIcon />} />
        </div>

        <Avatar
          src={getAvatar(userInfo?.info)}
          shape="circle"
          className="cursor-pointer"
          onClick={onClickAvatarAction}
          size={40}
        />
      </div>
    </div>
  );
}
