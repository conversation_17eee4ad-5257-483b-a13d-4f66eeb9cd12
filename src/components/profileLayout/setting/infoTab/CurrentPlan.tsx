'use client';

import Typography from '@/components/ui/typography/typography';
import { Button } from '../../../ui';
import BasicPlan from './BasicPlan';
import SubscribedPlan from './SubscribedPlan';

interface PaymentToken {
  id: string;
  gateway: string;
  tokenExpDate: string;
  cardNumber: string;
  cardType: string;
  bankCode?: string;
}

interface SubscriptionData {
  netAmount: number;
  taxAmount: number;
  nextBillingDate: string;
  canceledAt: string | null;
}

interface CurrentPlanProps {
  onCancelSubscription: () => void;
  onClickChangePaymentMethod?: () => void;
  planType?: 'subscribed' | 'basic';
  paymentToken?: PaymentToken;
  subscriptionData?: SubscriptionData;
}

const CurrentPlan = ({
  onCancelSubscription,
  onClickChangePaymentMethod,
  planType = 'basic',
  paymentToken,
  subscriptionData,
}: CurrentPlanProps) => {
  const isBasicPlan = planType === 'basic';

  // Helper function to get card type image
  const getCardImage = (cardType: any) => {
    switch (cardType?.toLowerCase()) {
      case 'visa':
        return '/images/payment/visa.png';
      case 'mastercard':
        return '/images/payment/mastercard.png';
      case 'jcb':
        return '/images/payment/jcb.png';
      case 'atm':
        return '/images/payment/atm.png';
      default:
        return null;
    }
  };

  // Helper function to format expiry date
  const formatExpiryDate = (tokenExpDate: string) => {
    const date = new Date(tokenExpDate);
    const month = date.toLocaleString('en-US', { month: 'long' });
    const year = date.getFullYear();
    return `Expires ${month} ${year}`;
  };
  return (
    <div className="grid grid-cols-1 gap-6 md:grid-cols-12">
      {/* Plan Card */}
      <div className="md:col-span-8">
        {isBasicPlan ? (
          <BasicPlan />
        ) : (
          <SubscribedPlan onCancelSubscription={onCancelSubscription} subscriptionData={subscriptionData} />
        )}
      </div>

      {/* Payment Method Card - Only show if not basic plan and subscription is not canceled */}
      {!isBasicPlan && !subscriptionData?.canceledAt && (
        <div className="flex flex-col md:col-span-4">
          <Typography variant="headlineSm" className="text-gray-900 mb-4">
            Phương thức thanh toán
          </Typography>
          <div className="flex flex-1 flex-col justify-between rounded-md border border-neutral-200 bg-neutral-50">
            <div className="border-gray-200 mb-4 items-center rounded-lg p-6">
              <div className="flex items-center">
                {getCardImage(paymentToken?.bankCode || '') && (
                  <div className="mr-4 flex h-12 w-16 items-center justify-center rounded border border-white">
                    <img src={getCardImage(paymentToken?.bankCode) as any} alt={paymentToken?.cardType || 'card'} />
                  </div>
                )}
                <div className="flex-1">
                  <Typography variant="headlineXs">
                    {paymentToken?.cardType?.toUpperCase() || 'Card'} ••••{' '}
                    {paymentToken?.cardNumber?.slice(-4) || '0000'}
                  </Typography>
                </div>
              </div>
              <Typography variant="labelMd">
                {paymentToken?.tokenExpDate ? formatExpiryDate(paymentToken.tokenExpDate) : 'No expiry date'}
              </Typography>
            </div>
            <div className="p-6">
              <Button variant="tertiary" className="w-full" onClick={onClickChangePaymentMethod}>
                Change payment method
              </Button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default CurrentPlan;
