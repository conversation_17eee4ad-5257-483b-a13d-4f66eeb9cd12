'use client';

import Button from '@/components/ui/button/button';
import Typography from '@/components/ui/typography/typography';
import { useGetTransactionReceipt } from '@/hooks/apis/payment';
import { XMarkIcon } from '@heroicons/react/24/outline';
import { Modal } from 'antd';

interface ReceiptModalProps {
  isOpen: boolean;
  onClose: () => void;
  transaction?: any;
}

const ReceiptModal = ({ isOpen, onClose, transaction }: ReceiptModalProps) => {
  // Fetch receipt data when modal is open and transaction is available
  const { data: receipt, isLoading } = useGetTransactionReceipt(
    transaction?.subscription?.id,
    transaction?.id,
    isOpen && !!transaction
  );

  // Helper function to format currency
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('vi-VN', {
      style: 'decimal',
      minimumFractionDigits: 0,
    }).format(amount);
  };

  // Helper function to format date
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('vi-VN');
  };
  return (
    <Modal
      open={isOpen}
      onCancel={onClose}
      footer={null}
      closable={false}
      centered
      width={600}
      className="receipt-modal"
    >
      <div className="flex flex-col">
        {isLoading && (
          <div className="mb-4 text-center">
            <Typography variant="bodyMd" className="text-gray-600">
              Đang tải biên nhận...
            </Typography>
          </div>
        )}
        
        {/* Header */}
        <div className="border-gray-200 mb-6 flex items-center justify-between border-b pb-4">
          <Typography variant="headlineXs">
            Biên nhận {receipt?.displayName || '#STU-000000'}
          </Typography>
          <button onClick={onClose} className="hover:bg-gray-100 rounded-lg p-2">
            <XMarkIcon className="text-gray-500 h-5 w-5" />
          </button>
        </div>

        {/* Company Info */}
        <div className="mb-8 flex flex-col">
          <div className="mb-4 flex items-center">
            <div className="flex items-center">
              <img src="/images/logo.png" alt="logo" />
            </div>
          </div>

          <Typography variant="labelMd" className="text-gray-900 mb-2 font-semibold">
            CÔNG TY TNHH CÔNG NGHỆ GIÁO DỤC TƯƠNG TÁC STUDIFY
          </Typography>
          <Typography variant="bodySm" className="text-gray-600 mb-1">
            282 Phan Xích Long, Phường Cầu Kiều, TP.HCM
          </Typography>
          <Typography variant="bodySm" className="text-gray-600">
            <EMAIL>
          </Typography>
        </div>

        {/* Receipt Details */}
        <div className="mb-8 space-y-6">
          {/* Invoice Number and Date */}
          <div className="flex justify-between">
            <Typography variant="bodyMd" className="text-gray-600">
              Số hóa đơn:
            </Typography>
            <Typography variant="bodyMd" className="text-gray-900 font-medium">
              {receipt?.displayName || '#STU-000000'}
            </Typography>
          </div>

          <div className="flex justify-between">
            <Typography variant="bodyMd" className="text-gray-600">
              Ngày xuất hóa đơn:
            </Typography>
            <Typography variant="bodyMd" className="text-gray-900">
              {receipt?.createdAt ? formatDate(receipt.createdAt) : '--'}
            </Typography>
          </div>

          {/* Service Period */}
          <div className="flex justify-between">
            <Typography variant="bodyMd" className="text-gray-600">
              Hiệu lực
            </Typography>
            <Typography variant="bodyMd" className="text-gray-900">
              {receipt?.data?.startDate && receipt?.data?.endDate
                ? `${formatDate(receipt.data.startDate)} → ${formatDate(receipt.data.endDate)}`
                : '--'}
            </Typography>
          </div>

          {/* Plan Details */}
          <div className="flex justify-between">
            <Typography variant="bodyMd" className="text-gray-600">
              {receipt?.data?.metadata?.plan || 'Gói đăng ký'}
              {receipt?.data?.metadata?.discount && ` (Giảm giá: ${receipt.data.metadata.discount})`}
            </Typography>
            <Typography variant="bodyMd" className="text-gray-900">
              {receipt?.netAmount ? `${formatCurrency(receipt.netAmount)} đ` : '--'}
            </Typography>
          </div>

          {/* Tax */}
          <div className="flex justify-between">
            <Typography variant="bodyMd" className="text-gray-600">
              Thuế GTGT
            </Typography>
            <Typography variant="bodyMd" className="text-gray-900">
              {receipt?.taxAmount ? `${formatCurrency(receipt.taxAmount)} đ` : '--'}
            </Typography>
          </div>

          {/* Total */}
          <div className="border-gray-200 border-t pt-4">
            <div className="flex justify-between">
              <Typography variant="headlineXs" className="text-gray-900 font-semibold">
                Total
              </Typography>
              <Typography variant="headlineXs" className="text-gray-900 font-semibold">
                {receipt ? `${formatCurrency((receipt.netAmount || 0) + (receipt.taxAmount || 0))} đ` : '--'}
              </Typography>
            </div>
          </div>

          {/* Payment Method */}
          <div className="flex justify-between">
            <Typography variant="bodyMd" className="text-gray-600">
              Phương thức thanh toán
            </Typography>
            <Typography variant="bodyMd" className="text-gray-900">
              {receipt?.gateway || '--'}
            </Typography>
          </div>
        </div>

        {/* Close Button */}
        <div className="flex justify-end">
          <Button variant="secondary" onClick={onClose} className="px-6">
            Đóng
          </Button>
        </div>
      </div>
    </Modal>
  );
};

export default ReceiptModal;
