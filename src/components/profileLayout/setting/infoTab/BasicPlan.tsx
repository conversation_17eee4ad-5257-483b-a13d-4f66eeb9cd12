'use client';

import Button from '@/components/ui/button/button';
import Typography from '@/components/ui/typography/typography';
import { useRouter } from 'next/navigation';

const BasicPlan = () => {
  const router = useRouter();

  const handleUpgrade = () => {
    router.push('/payment/billing-cycle');
  };
  return (
    <div className="relative overflow-hidden rounded-xl text-neutral-800">
      <Typography variant="headlineSm" className="text-neutral-800">
        Gói cơ bản của bạn
      </Typography>
      <div className="relative pt-4">
        <img src="/images/payment/basic_plan.png" alt="Basic Plan" className="w-full" />
        <div className="absolute left-0 top-0 z-[99] size-full">
          <div className="z-[99] m-auto flex size-[90%] flex-col justify-between pt-10">
            <div className="">
              <Typography variant="headlineXs" className="mb-2 text-neutral-700">
                Start your journey of self-discovery.
              </Typography>

              <ul className="mb-6 space-y-2 pt-3 text-sm">
                <li>
                  <Typography variant="labelMd" className="text-neutral-700">
                    • You can access <span className="font-semibold">free preview lessons</span> in every course.
                  </Typography>
                </li>
                <li>
                  <Typography variant="labelMd" className="text-neutral-700">
                    • Premium content is locked until you upgrade.
                  </Typography>
                </li>
              </ul>
            </div>
            <div className="flex items-center justify-between">
              <div className="mb-4">
                <Typography variant="labelMd" className="text-neutral-700">
                  Only <span className="font-semibold text-neutral-800">299,000đ/month</span>. Cancel anytime.
                  <br />
                  <span className="text-neutral-600">Secure payment via VNPay</span>
                </Typography>
              </div>

              <Button variant="primary-reversed" className="h-[40px]" onClick={handleUpgrade}>
                Đăng ký học ngay
              </Button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default BasicPlan;
