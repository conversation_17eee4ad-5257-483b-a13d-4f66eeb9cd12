'use client';

import Typography from '@/components/ui/typography/typography';
import { useGetTransactions } from '@/hooks/apis/payment';
import { CheckCircleIcon, XCircleIcon } from '@heroicons/react/20/solid';
import { Table } from 'antd';
import { ColumnsType } from 'antd/es/table';
import { useState } from 'react';
import ReceiptModal from './ReceiptModal';

interface TransactionTableData {
  key: string;
  date: string;
  package: string;
  amount: string;
  status: 'SUCCESS' | 'FAIL';
  originalTransaction: any; // For passing to modal
}

const TransactionTable = () => {
  const [isReceiptModalOpen, setIsReceiptModalOpen] = useState(false);
  const [selectedTransaction, setSelectedTransaction] = useState<any>(null);

  const { data: transactions, isLoading, error } = useGetTransactions();

  const handleViewReceipt = (transaction: any) => {
    setSelectedTransaction(transaction);
    setIsReceiptModalOpen(true);
  };

  const handleCloseReceiptModal = () => {
    setIsReceiptModalOpen(false);
    setSelectedTransaction(null);
  };

  // Transform API data to table format
  const transactionData: TransactionTableData[] =
    transactions?.map((transaction, index) => {
      const formatDate = (dateString: string) => {
        const date = new Date(dateString);
        return date.toLocaleDateString('vi-VN');
      };

      const getPackageType = (type: string) => {
        switch (type) {
          case 'INITIAL':
            return 'Đăng ký ban đầu';
          case 'RENEWAL':
            return 'Gia hạn';
          case 'REFUND':
            return 'Hoàn tiền';
          default:
            return type;
        }
      };

      return {
        key: transaction.id,
        date: formatDate(transaction.paidAt || transaction.createdAt),
        package: getPackageType(transaction.type),
        amount: `${transaction.amountTotal.toLocaleString('vi-VN')}đ`,
        status: transaction.status,
        originalTransaction: transaction,
      };
    }) || [];

  const getStatusDisplay = (status: string) => {
    switch (status) {
      case 'SUCCESS':
        return {
          icon: <CheckCircleIcon width={20} height={20} />,
          text: 'Thành công',
          className: 'bg-green-100 text-green-600',
        };
      case 'FAIL':
        return {
          icon: <XCircleIcon width={20} height={20} />,
          text: 'Thất bại',
          className: 'bg-red-100 text-red-600',
        };
      default:
        return {
          icon: <CheckCircleIcon width={20} height={20} />,
          text: status,
          className: 'bg-gray-100 text-gray-600',
        };
    }
  };

  // Table columns configuration
  const columns: ColumnsType<TransactionTableData> = [
    {
      title: 'Ngày thanh toán',
      dataIndex: 'date',
      key: 'date',
      render: (text: string) => (
        <Typography variant="bodyMd" className="text-neutral-900">
          {text}
        </Typography>
      ),
    },
    {
      title: 'Gói đăng ký',
      dataIndex: 'package',
      key: 'package',
      render: (text: string, row) => {
        return (
          <Typography variant="bodyMd" className="text-neutral-900">
            {row?.originalTransaction?.subscription?.planPrice?.name || '-'}
          </Typography>
        );
      },
    },
    {
      title: 'Số tiền',
      dataIndex: 'amount',
      key: 'amount',
      render: (text: string) => (
        <Typography variant="bodyMd" className="text-neutral-900">
          {text}
        </Typography>
      ),
    },
    {
      title: 'Trạng thái',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => {
        const statusDisplay = getStatusDisplay(status);
        return (
          <span className={`inline-flex items-center gap-2 rounded-full px-3 py-1 text-sm ${statusDisplay.className}`}>
            {statusDisplay.icon} {statusDisplay.text}
          </span>
        );
      },
    },
    {
      title: 'Biên nhận',
      key: 'actions',
      render: (_: any, record: TransactionTableData) => {
        if (record.status === 'SUCCESS') {
          return (
            <button
              className="text-blue-600 transition-colors hover:text-blue-800"
              onClick={() => handleViewReceipt(record.originalTransaction)}
            >
              <Typography variant="bodyMd" className="text-blue-600">
                Xem chi tiết
              </Typography>
            </button>
          );
        }
        return null;
      },
    },
  ];

  if (error) {
    return (
      <div>
        <Typography variant="headlineSm">Lịch sử giao dịch</Typography>
        <div className="mt-4 rounded-xl bg-white p-8 text-center">
          <Typography variant="bodyMd" className="text-red-600">
            Có lỗi xảy ra khi tải dữ liệu giao dịch
          </Typography>
        </div>
      </div>
    );
  }

  return (
    <div>
      <Typography variant="headlineSm">Lịch sử giao dịch</Typography>

      <div className="mt-4 rounded-xl bg-white">
        <Table
          columns={columns}
          dataSource={transactionData}
          pagination={false}
          loading={isLoading}
          locale={{
            emptyText: 'Chưa có giao dịch nào',
          }}
          className="w-full"
        />
      </div>

      <ReceiptModal isOpen={isReceiptModalOpen} onClose={handleCloseReceiptModal} transaction={selectedTransaction} />
    </div>
  );
};

export default TransactionTable;
