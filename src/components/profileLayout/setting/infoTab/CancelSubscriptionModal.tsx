'use client';

import Button from '@/components/ui/button/button';
import Typography from '@/components/ui/typography/typography';
import { useCancelSubscription } from '@/hooks/apis/payment';
import { Modal, message } from 'antd';

interface CancelSubscriptionModalProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
  subscriptionId?: string;
}

const CancelSubscriptionModal = ({ isOpen, onClose, onConfirm, subscriptionId }: CancelSubscriptionModalProps) => {
  const cancelSubscriptionMutation = useCancelSubscription();

  const handleCancelSubscription = async () => {
    if (!subscriptionId) {
      message.error('Không tìm thấy thông tin đăng ký');
      return;
    }

    try {
      await cancelSubscriptionMutation.mutateAsync(subscriptionId);
      message.success('Hủy đăng ký thành công');
      onClose();
      onConfirm(); // Call the original onConfirm to handle any additional logic
    } catch (error) {
      message.error('<PERSON><PERSON> lỗi xảy ra khi hủy đăng ký');
    }
  };
  return (
    <Modal
      open={isOpen}
      onCancel={onClose}
      footer={null}
      closable={true}
      centered
      width={687}
      className="cancel-subscription-modal"
    >
      <div className="flex flex-col p-4">
        <Typography variant="headlineMd" className="text-gray-900 mb-4">
          Hủy gói đăng kí?
        </Typography>

        <Typography variant="bodyMd" className="text-gray-600 mb-6">
          Bạn có chắc chắn muốn hủy đăng ký gói đăng kí. Các khóa học và tính năng thuộc gói này sẽ bị vô hiệu hoá.
        </Typography>

        <Typography variant="bodyMd" className="text-gray-600 mb-8">
          Bạn có muốn hủy gói?
        </Typography>

        <div className="flex gap-3">
          <Button 
            variant="tertiary" 
            onClick={handleCancelSubscription} 
            className="flex-1"
            loading={cancelSubscriptionMutation.isLoading}
            disabled={cancelSubscriptionMutation.isLoading}
          >
            Hủy gói
          </Button>
          <Button variant="primary" onClick={onClose} className="flex-1">
            Tiếp tục sử dụng
          </Button>
        </div>
      </div>
    </Modal>
  );
};

export default CancelSubscriptionModal;
