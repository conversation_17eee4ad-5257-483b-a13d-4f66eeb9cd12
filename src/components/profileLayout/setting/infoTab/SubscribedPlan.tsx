'use client';

import Button from '@/components/ui/button/button';
import Typography from '@/components/ui/typography/typography';

interface SubscriptionData {
  netAmount: number;
  taxAmount: number;
  nextBillingDate: string;
  canceledAt: string | null;
}

interface SubscribedPlanProps {
  onCancelSubscription: () => void;
  subscriptionData?: SubscriptionData;
}

const SubscribedPlan = ({ onCancelSubscription, subscriptionData }: SubscribedPlanProps) => {
  // Helper function to format currency
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('vi-VN', {
      style: 'currency',
      currency: 'VND',
      minimumFractionDigits: 0,
    }).format(amount);
  };

  // Helper function to format date
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('vi-VN', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
    });
  };

  // Calculate total amount (net + tax)
  const totalAmount = subscriptionData ? subscriptionData.netAmount + subscriptionData.taxAmount : 0;
  return (
    <div className="relative overflow-hidden rounded-xl text-white">
      <Typography variant="headlineSm">Gói đăng ký của bạn</Typography>
      <div className="relative pt-4">
        <img
          src="/images/payment/subscribed_plan.png"
          alt="Subscription Plan"
          className="w-full"
        />
        <div className="absolute left-0 top-0 z-[99] size-full">
          <div className="z-[99] m-auto flex size-[90%] flex-col justify-between pt-10">
            <div className="">
              <Typography variant="headlineXs" className="mb-2 text-secondary-400">
                Build. Scale. Own your future.
              </Typography>

              <ul className="mb-6 space-y-2 pt-3 text-sm">
                <li>
                  <Typography variant="labelMd" className="text-white">
                    • Designed for next-level learners & creators
                  </Typography>
                </li>
                <li>
                  <Typography variant="labelMd" className="text-white">
                    • Unlock full features to accelerate your learning journey.
                  </Typography>
                </li>
                <li>
                  <Typography variant="labelMd" className="text-white">
                    • Go beyond basics — build, teach, and scale your impact.
                  </Typography>
                </li>
              </ul>
            </div>
            <div className="flex items-center justify-between">
              {/* Show cancellation notice if subscription is canceled */}
              {subscriptionData?.canceledAt ? (
                <div className="mb-4">
                  <Typography variant="labelMd" className="text-white">
                    Gói đăng ký của bạn đã được hủy và sẽ hết hiệu lực vào{' '}
                    <span className="font-semibold text-white">
                      {subscriptionData?.nextBillingDate ? formatDate(subscriptionData.nextBillingDate) : 'N/A'}
                    </span>
                    . Bạn vẫn có thể sử dụng đầy đủ tính năng cho đến thời điểm đó.
                  </Typography>
                </div>
              ) : (
                <>
                  {/* Show next billing info for active subscriptions */}
                  <div className="mb-4">
                    <Typography variant="labelMd" className="text-white">
                      Hóa đơn tiếp theo của bạn có giá{' '}
                      <span className="font-semibold text-white">
                        {subscriptionData ? formatCurrency(totalAmount) : '0đ'}
                      </span>{' '}
                      vào{' '}
                      <span className="font-semibold text-white">
                        {subscriptionData?.nextBillingDate ? formatDate(subscriptionData.nextBillingDate) : 'N/A'}
                      </span>.
                    </Typography>
                  </div>

                  {/* Show cancel button for active subscriptions */}
                  <Button variant="secondary-reversed" onClick={onCancelSubscription}>
                    Hủy gói đăng ký
                  </Button>
                </>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SubscribedPlan;