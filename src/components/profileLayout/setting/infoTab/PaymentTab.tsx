'use client';

import VNPayPostForm from '@/components/payment/VNPayPostForm';
import { useChangePaymentMethod, useGetUserSubscriptions } from '@/hooks/apis/payment';
import { useState } from 'react';
import CancelSubscriptionModal from './CancelSubscriptionModal';
import CurrentPlan from './CurrentPlan';
import TransactionTable from './TransactionTable';

interface PaymentTabProps {
  hasSubscription: boolean;
}

const PaymentTab = ({ hasSubscription }: PaymentTabProps) => {
  const [isCancelModalOpen, setIsCancelModalOpen] = useState(false);

  // Get all subscriptions to check the last one
  const { data: _allSubscriptions, isLoading: _isLoading } = useGetUserSubscriptions();

  // Change payment method hook
  const {
    startChangePaymentMethod,
    paymentData,
    clearPaymentData,
    isLoading: isChangePaymentLoading,
  } = useChangePaymentMethod();

  // Check if the last subscription is active
  const lastSubscription = _allSubscriptions?.[0];
  const isLastSubscriptionActive = lastSubscription?.status === 'ACTIVE';

  // Get payment token and subscription data from the last subscription if it's active
  const activeSubscription = isLastSubscriptionActive ? lastSubscription : undefined;
  const planType = isLastSubscriptionActive ? 'subscribed' : 'basic';
  const paymentToken = activeSubscription?.paymentToken;
  const subscriptionData = activeSubscription
    ? {
        netAmount: activeSubscription.netAmount,
        taxAmount: activeSubscription.taxAmount,
        nextBillingDate: activeSubscription.nextBillingDate,
        canceledAt: activeSubscription.canceledAt,
      }
    : undefined;
  const handleCancelSubscription = () => {
    setIsCancelModalOpen(true);
  };

  const handleModalClose = () => {
    setIsCancelModalOpen(false);
  };

  const handleConfirmCancel = () => {
    // Handle actual cancellation logic here
    setIsCancelModalOpen(false);
  };

  const handleChangePaymentMethod = async () => {
    if (!activeSubscription?.id) {
      console.error('No active subscription found');
      return;
    }

    const requestData = {
      request_key: `${Date.now()}`,
      subscription_id: activeSubscription.id,
      locale: 'vn',
      add_data: 'custom-data',
    };

    try {
      await startChangePaymentMethod(requestData);
    } catch (error) {
      console.error('Failed to initiate payment method change:', error);
    }
  };

  // Show VNPay POST form if payment data with VNPay details is available
  if (paymentData?.ispTxnId) {
    return (
      <VNPayPostForm
        action="https://sandbox.vnpayment.vn/isp-svc/recurring-payment/pay"
        ispTxnId={paymentData.ispTxnId}
        tmnCode={paymentData.tmnCode!}
        dataKey={paymentData.dataKey!}
      />
    );
  }

  return (
    <div className="space-y-8">
      <CurrentPlan
        onCancelSubscription={handleCancelSubscription}
        onClickChangePaymentMethod={handleChangePaymentMethod}
        planType={planType as 'subscribed' | 'basic'}
        paymentToken={paymentToken}
        subscriptionData={subscriptionData}
      />

      <TransactionTable />

      <CancelSubscriptionModal
        isOpen={isCancelModalOpen}
        onClose={handleModalClose}
        onConfirm={handleConfirmCancel}
        subscriptionId={activeSubscription?.id}
      />
    </div>
  );
};

export default PaymentTab;
