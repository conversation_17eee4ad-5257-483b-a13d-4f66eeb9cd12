'use client';

import React from 'react';
import Modal, { ModalProps } from './modal';

const useModal = () => {
  const [open, setOpen] = React.useState(false);

  const show = () => {
    setOpen(true);
  };

  const hide = () => {
    setOpen(false);
  };

  const toggle = () => {
    setOpen((prev) => !prev);
  };

  const ModalComponent = (props: Omit<ModalProps, 'open' | 'onClose'>) => {
    return <Modal {...props} open={open} onClose={hide} />;
  };

  return { open, show, hide, toggle, Modal: ModalComponent };
};

export default useModal;
