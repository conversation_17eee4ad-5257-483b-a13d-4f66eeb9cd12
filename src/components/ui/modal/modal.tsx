'use client';

import { usePreventHydration } from '@/hooks';
import { cn } from '@/lib/utils';
import { Modal as AntModal, ModalProps as AntModalProps } from 'antd';
import { match } from 'ts-pattern';
import ModalTitle from './modal-title';
import './modal.scss';

type AntClassNames = { closeIcon?: string } & AntModalProps['classNames'];

type OmittedAntModalProps = Omit<AntModalProps, 'onCancel | closable | closeIcon | title | classNames'>;

export type ModalProps = {
  title?: string;
  classNames?: AntClassNames;
  type?: 'primary' | 'confirmation';
} & OmittedAntModalProps;

const getModalTypeClasses = (type: 'primary' | 'confirmation') => {
  return match(type)
    .with('primary', () => cn('p-6'))
    .with('confirmation', () => cn('px-6 py-4'))
    .exhaustive();
};

const getFooterContent = (props: OmittedAntModalProps) => {
  const hasFooterProps =
    props.footer ||
    props.okText ||
    props.cancelText ||
    props.okButtonProps ||
    props.cancelButtonProps ||
    props.onOk ||
    props.onCancel;
  const footerContent = hasFooterProps ? props.footer : null;
  return footerContent;
};

const Modal = (props: ModalProps) => {
  const { title, type = 'primary', children, onClose, className, ...rest } = props;

  usePreventHydration();

  const modalTypeClasses = getModalTypeClasses(type);

  const mergedClassNames = {
    ...rest.classNames,
    header: cn(modalTypeClasses, rest.classNames?.header),
  } satisfies AntModalProps['classNames'];

  const footerContent = getFooterContent(props);

  return (
    <AntModal
      width={560}
      centered
      {...rest}
      footer={footerContent}
      title={<ModalTitle title={title} onClose={onClose} iconClassName={rest.classNames?.closeIcon} />}
      className={cn('custom-modal rounded-lg', className)}
      classNames={mergedClassNames}
      onCancel={onClose}
      closeIcon={null}
      closable={false}
    >
      {children}
    </AntModal>
  );
};

export default Modal;
