'use client';

import { useEffect, useRef } from 'react';

interface VNPayPostFormProps {
  /** VNPay payment gateway URL */
  action: string;
  /** Transaction ID from VNPay response */
  ispTxnId: string;
  /** Merchant terminal code */
  tmnCode: string;
  /** Data key from VNPay response */
  dataKey: string;
}

/**
 * VNPayPostForm component for redirecting to VNPay card verification page
 * Based on VNPay Recurring Payment Technical Specification v2.1.0, Section 2.6
 */
const VNPayPostForm = ({ action, ispTxnId, tmnCode, dataKey }: VNPayPostFormProps) => {
  const formRef = useRef<HTMLFormElement>(null);

  useEffect(() => {
    // Auto-submit the form when component mounts
    if (formRef.current) {
      formRef.current.submit();
    }
  }, []);

  console.log('vnpayyy', action, ispTxnId, tmnCode, dataKey);

  return (
    <div className="flex min-h-screen items-center justify-center bg-neutral-50">
      <div className="rounded-lg bg-white p-8 shadow-lg">
        <div className="mb-4 text-center">
          <div className="mb-4">
            <img src="/images/payment/vnpay.png" alt="VNPay" className="mx-auto h-12" />
          </div>
          <h2 className="text-xl font-semibold text-neutral-900">Đang chuyển hướng đến VNPay</h2>
          <p className="mt-2 text-neutral-600">Vui lòng chờ trong giây lát...</p>
        </div>

        {/* Hidden form that auto-submits to VNPay */}
        <form 
          ref={formRef} 
          id="vnpay-redirect-form" 
          style={{ display: 'none' }} 
          method="POST" 
          action={action}
          encType="application/x-www-form-urlencoded"
        >
          <input type="hidden" name="ispTxnId" value={ispTxnId} />
          <input type="hidden" name="tmnCode" value={tmnCode} />
          <input type="hidden" name="dataKey" value={dataKey} />
        </form>

        {/* Loading indicator */}
        <div className="flex justify-center">
          <div className="h-8 w-8 animate-spin rounded-full border-4 border-neutral-200 border-t-blue-500"></div>
        </div>
      </div>
    </div>
  );
};

export default VNPayPostForm;
