'use client';

import { Typography } from '@/components/ui';
import { But<PERSON>, Modal } from 'antd';
import Image from 'next/image';
import React, { useEffect, useState } from 'react';

export type TutorialStepType = 'create' | 'design' | 'publish';

interface TutorialStepContent {
  title: string;
  subtitle: string;
  description: string;
  buttonText: string;
  stepNumber: number;
  icon: string;
}

interface TutorialStepsProps {
  isOpen: boolean;
  onClose: () => void;
  onComplete?: () => void;
}

const tutorialContent: Record<TutorialStepType, TutorialStepContent> = {
  create: {
    stepNumber: 1,
    title: 'Hãy bắt đầu hành trình tạo khóa học của bạn ngay bây giờ!',
    subtitle: 'Bước 1: Thiết lập',
    description: 'Đặt tiêu đề, mô tả và các thông tin cơ bản để học viên hiểu bạn sẽ mang đến điều gì thú vị.',
    buttonText: '<PERSON><PERSON><PERSON> đầu thiết lập khóa học',
    icon: '/images/step-setting.png',
  },
  design: {
    stepNumber: 2,
    title: 'Khóa học đã được khởi tạo thành công! Thêm nội dung theo cách của riêng bạn.',
    subtitle: 'Bước 2: Thiết kế',
    description: 'Thêm video, tài liệu, và câu hỏi tương tác để nội dung khóa học trở nên sinh động, dễ tiếp thu.',
    buttonText: 'Bắt đầu thiết lập khóa học',
    icon: '/images/step-design.png',
  },
  publish: {
    stepNumber: 3,
    title: 'Chia sẻ kiến thức của bạn đến nhiều người học hơn bao giờ hết.',
    subtitle: 'Bước 3: Xuất bản',
    description: 'Kiểm tra lại mọi thứ, thêm FAQs, và... BOOM! Khóa học của bạn đã sẵn sàng đến tay học viên.',
    buttonText: 'Bắt đầu thiết lập khóa học',
    icon: '/images/step-publish.png',
  },
};

export const TutorialSteps: React.FC<TutorialStepsProps> = ({ isOpen, onClose, onComplete }) => {
  const [currentStepIndex, setCurrentStepIndex] = useState(0);
  const tutorialTypes: TutorialStepType[] = ['create', 'design', 'publish'];
  const content = tutorialContent[tutorialTypes[currentStepIndex]];

  useEffect(() => {
    if (!isOpen) return;

    const interval = setInterval(() => {
      setCurrentStepIndex((prevIndex) => (prevIndex + 1) % tutorialTypes.length);
    }, 2000);

    return () => clearInterval(interval);
  }, [isOpen, tutorialTypes.length]);

  useEffect(() => {
    if (isOpen) {
      setCurrentStepIndex(0);
    }
  }, [isOpen]);

  const handleComplete = () => {
    onComplete?.();
    onClose();
  };

  return (
    <Modal
      open={isOpen}
      onCancel={onClose}
      title={null}
      width={558}
      height={570}
      centered
      closable={false}
      maskClosable={false}
      footer={null}
      className="tutorial-steps-modal"
      styles={{
        content: { padding: 0 },
      }}
    >
      <div className="flex flex-col items-center">
        <div className="w-full">
          <Image
            src={`/images/course-step-${content.stepNumber}.svg`}
            alt={`Step ${content.stepNumber}`}
            width={558}
            height={314}
            className="h-auto w-full object-cover"
            style={{ borderRadius: '10px 10px 0 0' }}
            quality={100}
            priority
            unoptimized={true}
          />
        </div>

        <div className="px-6 pt-6">
          <Typography variant="headlineXs" className="text-black">
            {content.title}
          </Typography>

          <div className="mb-1 flex gap-2">
            <div className="flex items-center gap-2 pt-6">
              <Image src={content.icon} alt={content.icon} width={16} height={16} />
              <Typography variant="titleMd" className="text-black">
                {content.subtitle}
              </Typography>
            </div>
          </div>
          <div className="mb-4 flex gap-2">
            <div className="flex items-center gap-2">
              <div
                style={{
                  width: '2px',
                  height: '40px',
                  backgroundColor: '#E6EAF0',
                  marginLeft: '7px',
                  marginRight: '7px',
                  marginTop: '0',
                  marginBottom: '0',
                  borderRadius: '1px',
                }}
              />

              <Typography variant="bodyMd" className="text-black">
                {content.description}
              </Typography>
            </div>
          </div>
        </div>

        <div className="flex gap-3 px-6 pb-6">
          <Button type="primary" onClick={handleComplete} className="px-6">
            <Typography variant="labelLg" className="text-white">
              {content.buttonText}
            </Typography>
          </Button>
        </div>
      </div>
    </Modal>
  );
};

export default TutorialSteps;
