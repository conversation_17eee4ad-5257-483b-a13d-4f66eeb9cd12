import { Button, Result } from 'antd';
import Link from 'next/link';

interface NotFoundProps {
  title?: string;
  goBackLink?: string;
}
function NotFound({ title = 'Không tìm thấy khóa học', goBackLink }: NotFoundProps) {
  return (
    <Result
      title={title}
      status={404}
      extra={
        <Link href={goBackLink ? goBackLink : '/'}>
          <Button type={'primary'}>Trở về</Button>
        </Link>
      }
    />
  );
}

export default NotFound;
