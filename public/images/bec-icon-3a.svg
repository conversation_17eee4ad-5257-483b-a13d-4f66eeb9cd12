<svg width="200" height="200" viewBox="0 0 200 200" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_d_780_4514)">
<g filter="url(#filter1_i_780_4514)">
<path d="M75 37.5C75 30.5964 80.5964 25 87.5 25H162.5C169.404 25 175 30.5964 175 37.5V137.5C175 144.404 169.404 150 162.5 150H87.5C80.5964 150 75 144.404 75 137.5V37.5Z" fill="url(#paint0_linear_780_4514)"/>
</g>
<foreignObject x="22.1484" y="33.5" width="140.082" height="59.335"><div xmlns="http://www.w3.org/1999/xhtml" style="backdrop-filter:blur(2px);clip-path:url(#bgblur_0_780_4514_clip_path);height:100%;width:100%"></div></foreignObject><g filter="url(#filter2_ii_780_4514)" data-figma-bg-blur-radius="4">
<path fill-rule="evenodd" clip-rule="evenodd" d="M26.6785 42.3584C25.294 40.2817 26.7828 37.5 29.2787 37.5H57.5947C59.6844 37.5 61.6358 38.5444 62.795 40.2831L86.178 75.3577C75.1773 76.6759 65.2745 81.566 57.6629 88.8349L26.6785 42.3584ZM126.716 88.8349L157.701 42.3584C159.085 40.2817 157.596 37.5 155.1 37.5H126.784C124.695 37.5 122.743 38.5444 121.584 40.2831L98.2011 75.3577C109.202 76.6759 119.105 81.566 126.716 88.8349Z" fill="white" fill-opacity="0.4"/>
</g>
<foreignObject x="38.1875" y="71" width="108" height="108"><div xmlns="http://www.w3.org/1999/xhtml" style="backdrop-filter:blur(2px);clip-path:url(#bgblur_1_780_4514_clip_path);height:100%;width:100%"></div></foreignObject><g filter="url(#filter3_ii_780_4514)" data-figma-bg-blur-radius="4">
<path fill-rule="evenodd" clip-rule="evenodd" d="M92.1875 175C119.802 175 142.188 152.614 142.188 125C142.188 97.3858 119.802 75 92.1875 75C64.5733 75 42.1875 97.3858 42.1875 125C42.1875 152.614 64.5733 175 92.1875 175ZM92.1875 162.5C112.898 162.5 129.688 145.711 129.688 125C129.688 104.289 112.898 87.5 92.1875 87.5C71.4768 87.5 54.6875 104.289 54.6875 125C54.6875 145.711 71.4768 162.5 92.1875 162.5Z" fill="white" fill-opacity="0.4"/>
</g>
<g filter="url(#filter4_ii_780_4514)">
<path d="M123.438 125C123.438 142.259 109.446 156.25 92.1875 156.25C74.9286 156.25 60.9375 142.259 60.9375 125C60.9375 107.741 74.9286 93.75 92.1875 93.75C109.446 93.75 123.438 107.741 123.438 125Z" fill="white" fill-opacity="0.6"/>
</g>
</g>
<defs>
<filter id="filter0_d_780_4514" x="18.1484" y="17" width="172.852" height="174" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="4" dy="4"/>
<feGaussianBlur stdDeviation="6"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.934214 0 0 0 0 0.814051 0 0 0 0 0.14648 0 0 0 0.35 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_780_4514"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_780_4514" result="shape"/>
</filter>
<filter id="filter1_i_780_4514" x="75" y="25" width="100" height="125" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="4"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.8 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_780_4514"/>
</filter>
<filter id="filter2_ii_780_4514" x="22.1484" y="33.5" width="140.082" height="59.335" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 1 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_780_4514"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="4"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 1 0"/>
<feBlend mode="normal" in2="effect1_innerShadow_780_4514" result="effect2_innerShadow_780_4514"/>
</filter>
<clipPath id="bgblur_0_780_4514_clip_path" transform="translate(-22.1484 -33.5)"><path fill-rule="evenodd" clip-rule="evenodd" d="M26.6785 42.3584C25.294 40.2817 26.7828 37.5 29.2787 37.5H57.5947C59.6844 37.5 61.6358 38.5444 62.795 40.2831L86.178 75.3577C75.1773 76.6759 65.2745 81.566 57.6629 88.8349L26.6785 42.3584ZM126.716 88.8349L157.701 42.3584C159.085 40.2817 157.596 37.5 155.1 37.5H126.784C124.695 37.5 122.743 38.5444 121.584 40.2831L98.2011 75.3577C109.202 76.6759 119.105 81.566 126.716 88.8349Z"/>
</clipPath><filter id="filter3_ii_780_4514" x="38.1875" y="71" width="108" height="108" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 1 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_780_4514"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="4"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 1 0"/>
<feBlend mode="normal" in2="effect1_innerShadow_780_4514" result="effect2_innerShadow_780_4514"/>
</filter>
<clipPath id="bgblur_1_780_4514_clip_path" transform="translate(-38.1875 -71)"><path fill-rule="evenodd" clip-rule="evenodd" d="M92.1875 175C119.802 175 142.188 152.614 142.188 125C142.188 97.3858 119.802 75 92.1875 75C64.5733 75 42.1875 97.3858 42.1875 125C42.1875 152.614 64.5733 175 92.1875 175ZM92.1875 162.5C112.898 162.5 129.688 145.711 129.688 125C129.688 104.289 112.898 87.5 92.1875 87.5C71.4768 87.5 54.6875 104.289 54.6875 125C54.6875 145.711 71.4768 162.5 92.1875 162.5Z"/>
</clipPath><filter id="filter4_ii_780_4514" x="60.9375" y="93.75" width="62.5" height="66.5" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 1 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_780_4514"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="4"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 1 0"/>
<feBlend mode="normal" in2="effect1_innerShadow_780_4514" result="effect2_innerShadow_780_4514"/>
</filter>
<linearGradient id="paint0_linear_780_4514" x1="125" y1="25" x2="125" y2="150" gradientUnits="userSpaceOnUse">
<stop stop-color="#FCE14B"/>
<stop offset="1" stop-color="#F4BD5D"/>
</linearGradient>
</defs>
</svg>
