<svg width="200" height="200" viewBox="0 0 200 200" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_d_780_4513)">
<g filter="url(#filter1_i_780_4513)">
<path d="M50 53.125C50 46.2214 55.5964 40.625 62.5 40.625H162.5C169.404 40.625 175 46.2214 175 53.125V121.875C175 128.779 169.404 134.375 162.5 134.375H62.5C55.5964 134.375 50 128.779 50 121.875V53.125Z" fill="url(#paint0_linear_780_4513)"/>
</g>
<foreignObject x="21" y="61.625" width="133" height="101.75"><div xmlns="http://www.w3.org/1999/xhtml" style="backdrop-filter:blur(2px);clip-path:url(#bgblur_0_780_4513_clip_path);height:100%;width:100%"></div></foreignObject><g filter="url(#filter2_ii_780_4513)" data-figma-bg-blur-radius="4">
<path d="M25 78.125C25 71.2214 30.5964 65.625 37.5 65.625H137.5C144.404 65.625 150 71.2214 150 78.125V146.875C150 153.779 144.404 159.375 137.5 159.375H37.5C30.5964 159.375 25 153.779 25 146.875V78.125Z" fill="white" fill-opacity="0.4"/>
</g>
<g filter="url(#filter3_ii_780_4513)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M37.5 65.625H137.5C144.404 65.625 150 71.2214 150 78.125V96.875L92.1424 120.018C89.1622 121.21 85.8378 121.21 82.8576 120.018L25 96.875V78.125C25 71.2214 30.5964 65.625 37.5 65.625ZM87.5 109.375C90.9518 109.375 93.75 106.577 93.75 103.125C93.75 99.6732 90.9518 96.875 87.5 96.875C84.0482 96.875 81.25 99.6732 81.25 103.125C81.25 106.577 84.0482 109.375 87.5 109.375Z" fill="white" fill-opacity="0.6"/>
</g>
<g filter="url(#filter4_ii_780_4513)">
<path d="M101.215 65.625C99.7935 59.3625 94.193 54.6875 87.5005 54.6875C80.8081 54.6875 75.2075 59.3625 73.7865 65.625H64.2695C65.7973 54.1583 75.6159 45.3125 87.5005 45.3125C99.3851 45.3125 109.204 54.1583 110.731 65.625H101.215Z" fill="white" fill-opacity="0.6"/>
</g>
</g>
<defs>
<filter id="filter0_d_780_4513" x="21" y="36.625" width="166" height="134.75" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="4" dy="4"/>
<feGaussianBlur stdDeviation="4"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.117647 0 0 0 0 0.278431 0 0 0 0 1 0 0 0 0.2 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_780_4513"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_780_4513" result="shape"/>
</filter>
<filter id="filter1_i_780_4513" x="50" y="40.625" width="125" height="93.75" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="4"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.8 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_780_4513"/>
</filter>
<filter id="filter2_ii_780_4513" x="21" y="61.625" width="133" height="101.75" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 1 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_780_4513"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="4"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 1 0"/>
<feBlend mode="normal" in2="effect1_innerShadow_780_4513" result="effect2_innerShadow_780_4513"/>
</filter>
<clipPath id="bgblur_0_780_4513_clip_path" transform="translate(-21 -61.625)"><path d="M25 78.125C25 71.2214 30.5964 65.625 37.5 65.625H137.5C144.404 65.625 150 71.2214 150 78.125V146.875C150 153.779 144.404 159.375 137.5 159.375H37.5C30.5964 159.375 25 153.779 25 146.875V78.125Z"/>
</clipPath><filter id="filter3_ii_780_4513" x="25" y="65.625" width="125" height="59.2871" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 1 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_780_4513"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="4"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 1 0"/>
<feBlend mode="normal" in2="effect1_innerShadow_780_4513" result="effect2_innerShadow_780_4513"/>
</filter>
<filter id="filter4_ii_780_4513" x="64.2695" y="45.3125" width="46.4609" height="24.3125" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 1 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_780_4513"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="4"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 1 0"/>
<feBlend mode="normal" in2="effect1_innerShadow_780_4513" result="effect2_innerShadow_780_4513"/>
</filter>
<linearGradient id="paint0_linear_780_4513" x1="112.5" y1="40.625" x2="112.5" y2="134.375" gradientUnits="userSpaceOnUse">
<stop stop-color="#0E8FFF"/>
<stop offset="1" stop-color="#1E47FF"/>
</linearGradient>
</defs>
</svg>
