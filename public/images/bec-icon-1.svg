<svg width="200" height="200" viewBox="0 0 200 200" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_d_965_3736)">
<g filter="url(#filter1_i_965_3736)">
<path d="M50 53.125C50 46.2214 55.5964 40.625 62.5 40.625H162.5C169.404 40.625 175 46.2214 175 53.125V121.875C175 128.779 169.404 134.375 162.5 134.375H62.5C55.5964 134.375 50 128.779 50 121.875V53.125Z" fill="#435066"/>
</g>
<foreignObject x="21" y="61.625" width="133" height="101.75"><div xmlns="http://www.w3.org/1999/xhtml" style="backdrop-filter:blur(2px);clip-path:url(#bgblur_0_965_3736_clip_path);height:100%;width:100%"></div></foreignObject><g filter="url(#filter2_ii_965_3736)" data-figma-bg-blur-radius="4">
<path fill-rule="evenodd" clip-rule="evenodd" d="M37.5 65.625C30.5964 65.625 25 71.2214 25 78.125V146.875C25 153.779 30.5964 159.375 37.5 159.375H137.5C144.404 159.375 150 153.779 150 146.875V78.125C150 71.2214 144.404 65.625 137.5 65.625H37.5ZM71.875 98.062V126.938C71.875 129.057 74.2501 130.377 76.1414 129.309L101.718 114.871C103.594 113.812 103.594 111.188 101.718 110.129L76.1414 95.6906C74.2501 94.623 71.875 95.9432 71.875 98.062Z" fill="white" fill-opacity="0.4"/>
</g>
<g filter="url(#filter3_ii_965_3736)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M43.75 75C38.5723 75 34.375 79.1973 34.375 84.375V140.625C34.375 145.803 38.5723 150 43.75 150H131.25C136.428 150 140.625 145.803 140.625 140.625V84.375C140.625 79.1973 136.428 75 131.25 75H43.75ZM71.875 98.062V126.938C71.875 129.057 74.2501 130.377 76.1414 129.309L101.718 114.871C103.594 113.812 103.594 111.188 101.718 110.129L76.1414 95.6906C74.2501 94.623 71.875 95.9432 71.875 98.062Z" fill="white" fill-opacity="0.6"/>
</g>
</g>
<defs>
<filter id="filter0_d_965_3736" x="14" y="29.625" width="180" height="148.75" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="4" dy="4"/>
<feGaussianBlur stdDeviation="7.5"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_965_3736"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_965_3736" result="shape"/>
</filter>
<filter id="filter1_i_965_3736" x="50" y="40.625" width="125" height="93.75" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="4"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.8 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_965_3736"/>
</filter>
<filter id="filter2_ii_965_3736" x="21" y="61.625" width="133" height="101.75" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 1 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_965_3736"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="4"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 1 0"/>
<feBlend mode="normal" in2="effect1_innerShadow_965_3736" result="effect2_innerShadow_965_3736"/>
</filter>
<clipPath id="bgblur_0_965_3736_clip_path" transform="translate(-21 -61.625)"><path fill-rule="evenodd" clip-rule="evenodd" d="M37.5 65.625C30.5964 65.625 25 71.2214 25 78.125V146.875C25 153.779 30.5964 159.375 37.5 159.375H137.5C144.404 159.375 150 153.779 150 146.875V78.125C150 71.2214 144.404 65.625 137.5 65.625H37.5ZM71.875 98.062V126.938C71.875 129.057 74.2501 130.377 76.1414 129.309L101.718 114.871C103.594 113.812 103.594 111.188 101.718 110.129L76.1414 95.6906C74.2501 94.623 71.875 95.9432 71.875 98.062Z"/>
</clipPath><filter id="filter3_ii_965_3736" x="34.375" y="75" width="106.25" height="79" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 1 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_965_3736"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="4"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 1 0"/>
<feBlend mode="normal" in2="effect1_innerShadow_965_3736" result="effect2_innerShadow_965_3736"/>
</filter>
</defs>
</svg>
