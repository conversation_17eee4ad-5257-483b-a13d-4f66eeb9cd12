import type { Config } from 'tailwindcss';

import { boxShadow } from 'tailwindcss/defaultTheme';

import tailwindTypography from '@tailwindcss/typography';
import { palette } from './src/config/theme/palette';

const config: Config = {
  important: true,
  content: [
    './src/pages/**/*.{js,ts,jsx,tsx,mdx}',
    './src/components/**/*.{js,ts,jsx,tsx,mdx}',
    './src/**/**/*.{js,jsx,ts,tsx}',
    './src/**/**/**/*.{js,jsx,ts,tsx}',
    './src/**/**/**/**/*.{js,jsx,ts,tsx}',
    './src/**/**/**/**/**/*.{js,jsx,ts,tsx}',
    './src/**/**/**/**/**/**/*.{js,jsx,ts,tsx}',
    './src/**/**/**/**/**/**/**/*.{js,jsx,ts,tsx}',
    './src/**/**/**/**/**/**/**/**/*.{js,jsx,ts,tsx}',
    './src/app/**/*.{js,ts,jsx,tsx,mdx}',
  ],
  theme: {
    fontFamily: {
      poppins: ['SVN-Poppins', 'sans-serif'],
      nunito: ['Nunito', 'sans-serif'],
    },
    fontSize: {
      // TODO: update font size after revamp the creator UI
      xxs: ['0.625rem', { lineHeight: '0.75rem' }],
      xs: ['0.75rem', { lineHeight: '1rem' }],
      sm: ['0.875rem', { lineHeight: '1.25rem' }],
      base: ['1rem', { lineHeight: '1.5rem' }],
      lg: ['1.125rem', { lineHeight: '1.75rem' }],
      xl: ['1.25rem', { lineHeight: '2rem' }],
      '2xl': ['1.5rem', { lineHeight: '2.5rem' }],
      '3xl': ['2rem', { lineHeight: '2.5rem' }],
      '4xl': ['2.5rem', { lineHeight: '3rem' }],
      '5xl': ['3rem', { lineHeight: '3.5rem' }],
      '6xl': ['3.75rem', { lineHeight: '1' }],
      '7xl': ['4.5rem', { lineHeight: '1' }],
      '8xl': ['6rem', { lineHeight: '1' }],
      '9xl': ['8rem', { lineHeight: '1' }],
    },
    colors: {
      primary: palette.primary,
      secondary: palette.secondary,
      neutral: palette.neutral,
      blue: palette.blue,
      green: palette.green,
      yellow: palette.yellow,
      red: palette.red,
      purple: palette.purple,
      pink: palette.pink,
      orange: palette.orange,
    },
    screens: {
      xs: '600px',
      sm: '900px',
      md: '1200px',
      lg: '1800px',
    },
    boxShadow: {
      ...boxShadow,
      light: '0px 2px 4px 0px #0000001F',
      medium: '0px 4px 8px 0px #0000002E',
      dark: '0px 6px 16px 0px #00000033',
    },
    container: {
      center: true,
      padding: '1.5rem',
    },
    extend: {
      fontFamily: {
        poppins: ['SVN-Poppins', 'sans-serif'],
        nunito: ['Nunito', 'sans-serif'],
      },
      maxWidth: {
        '8xl': '1440px',
      },
      backgroundImage: {
        banner: "url('/images/background_banner.webp')",
      },
      fontSize: {
        'display-xl': ['84px', { lineHeight: '132px', fontWeight: 500, letterSpacing: '-2px' }],
        'display-lg': ['58px', { lineHeight: '64px', fontWeight: 700, letterSpacing: '-0.25px' }],
        'display-md': ['45px', { lineHeight: '52px', fontWeight: 700, letterSpacing: '-0.25px' }],
        'display-sm': ['36px', { lineHeight: '44px', fontWeight: 700, letterSpacing: '-0.25px' }],

        'headline-lg': ['32px', { lineHeight: '40px', fontWeight: 600, letterSpacing: '-0.1px' }],
        'headline-md': ['28px', { lineHeight: '36px', fontWeight: 600, letterSpacing: '-0.1px' }],
        'headline-sm': ['24px', { lineHeight: '32px', fontWeight: 600, letterSpacing: '-0.1px' }],
        'headline-xs': ['20px', { lineHeight: '28px', fontWeight: 600, letterSpacing: '0px' }],

        'title-lg': ['22px', { lineHeight: '40px', fontWeight: 700, letterSpacing: '0px' }],
        'title-md': ['16px', { lineHeight: '24px', fontWeight: 700, letterSpacing: '0.1px' }],
        'title-sm': ['14px', { lineHeight: '20px', fontWeight: 700, letterSpacing: '0.15px' }],

        'label-lg': ['16px', { lineHeight: '24px', fontWeight: 500, letterSpacing: '0px' }],
        'label-md': ['14px', { lineHeight: '20px', fontWeight: 500, letterSpacing: '0.1px' }],
        'label-sm': ['12px', { lineHeight: '16px', fontWeight: 500, letterSpacing: '0.25px' }],
        'label-xs': ['11px', { lineHeight: '20px', fontWeight: 500, letterSpacing: '0.25px' }],

        'body-lg': ['16px', { lineHeight: '24px', fontWeight: 500, letterSpacing: '0px' }],
        'body-md': ['14px', { lineHeight: '20px', fontWeight: 500, letterSpacing: '0.1px' }],
        'body-sm': ['12px', { lineHeight: '16px', fontWeight: 500, letterSpacing: '0.1px' }],
      },
      colors: {
        white: '#FFFFFF',
        black: '#000000',
        transparent: 'transparent',

        'base-white-100': palette.base['white-100'],
        'base-white-75': palette.base['white-75'],
        'base-white-50': palette.base['white-50'],
        'base-white-25': palette.base['white-25'],
        'base-white-10': palette.base['white-10'],

        'base-black-100': palette.base['black-100'],
        'base-black-75': palette.base['black-75'],
        'base-black-50': palette.base['black-50'],
        'base-black-25': palette.base['black-25'],
        'base-black-10': palette.base['black-10'],

        success: palette.green[700],
        error: palette.red[500],
        warning: palette.yellow[500],
        information: palette.blue[500],

        ...palette.text,

        'ink-700': '#4F5261',
        'ink-600': '#65697B',
        'ink-200': '#CACCD4',
        'ink-400': '#969AAA',
        'ink-100': '#E5E5E9',
        'ink-black': '#0F1013',
        'ink-800': '#3B3D48',
        'ink-white': '#EBEBFF',
      },
      // borderRadius: {
      //   md: '12px',
      //   sm: '8px',
      //   '4xl': '48px',
      // },
    },
  },

  plugins: [tailwindTypography],
} satisfies Config;

export default config;
